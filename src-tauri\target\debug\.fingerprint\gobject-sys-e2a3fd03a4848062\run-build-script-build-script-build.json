{"rustc": 15597765236515928571, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[15885457518084958445, "build_script_build", false, 4707876535380780703]], "local": [{"RerunIfEnvChanged": {"var": "GOBJECT_2.0_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSROOT", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_BUILD_INTERNAL", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_LINK", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_GOBJECT_2_0_LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_GOBJECT_2_0_LIB_FRAMEWORK", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_GOBJECT_2_0_SEARCH_NATIVE", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_GOBJECT_2_0_SEARCH_FRAMEWORK", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_GOBJECT_2_0_INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_GOBJECT_2_0_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_GOBJECT_2_0_BUILD_INTERNAL", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSTEM_DEPS_GOBJECT_2_0_LINK", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}