cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rerun-if-changed=tauri.conf.json
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=dev
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_tauri
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=E:\dev\dev_2025\site-z\src-tauri\target\debug\build\site-z-ecfe1548ba4683c4\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=E:\dev\dev_2025\site-z\src-tauri\target\debug\build\site-z-ecfe1548ba4683c4\out\resource.lib
