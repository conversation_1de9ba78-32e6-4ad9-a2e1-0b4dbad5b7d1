{"rustc": 15597765236515928571, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2225463790103693989, "path": 2435193238880804786, "deps": [[2950323987832879288, "miniz_oxide", false, 12064905653288685578], [5466618496199522463, "crc32fast", false, 13944361256459307944]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-a4874c39407edb8e/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}