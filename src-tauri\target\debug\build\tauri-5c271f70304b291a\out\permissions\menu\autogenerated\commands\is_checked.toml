# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-checked"
description = "Enables the is_checked command without any pre-configured scope."
commands.allow = ["is_checked"]

[[permission]]
identifier = "deny-is-checked"
description = "Denies the is_checked command without any pre-configured scope."
commands.deny = ["is_checked"]
