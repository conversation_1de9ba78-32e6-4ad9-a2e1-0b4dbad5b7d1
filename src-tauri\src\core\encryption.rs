// src/core/encryption.rs
// AES-256-CBC 加密/解密功能

use aes::Aes256;
use cbc::{Decryptor, Encryptor};
use cbc::cipher::{block_padding::Pkcs7, KeyIvInit, BlockEncryptMut, BlockDecryptMut};
use rand::Rng;
use crate::errors::AppError;

type Aes256CbcEnc = Encryptor<Aes256>;
type Aes256CbcDec = Decryptor<Aes256>;

#[derive(Clone)]
pub struct EncryptionManager {
    key: [u8; 32], // 256位密钥
}

impl EncryptionManager {
    /// 从十六进制字符串创建加密管理器
    pub fn new(key_hex: &str) -> Result<Self, AppError> {
        let key_bytes = hex::decode(key_hex)
            .map_err(|_| AppError::DecryptionError("Invalid key format".to_string()))?;

        if key_bytes.len() != 32 {
            return Err(AppError::DecryptionError("Key must be 32 bytes (64 hex chars)".to_string()));
        }

        let mut key = [0u8; 32];
        key.copy_from_slice(&key_bytes);

        Ok(Self { key })
    }

    /// 加密数据
    /// 返回格式：IV(32位十六进制) + 加密数据(十六进制)
    pub fn encrypt(&self, plaintext: &str) -> Result<String, AppError> {
        // 生成随机IV
        let mut iv = [0u8; 16];
        rand::thread_rng().fill(&mut iv);

        // 准备数据进行加密，预留填充空间
        let data = plaintext.as_bytes();
        let mut buffer = vec![0u8; data.len() + 16]; // 预留填充空间
        buffer[..data.len()].copy_from_slice(data);

        // 创建加密器
        let cipher = Aes256CbcEnc::new(&self.key.into(), &iv.into());

        // 进行加密（使用PKCS7填充）
        let ciphertext = cipher.encrypt_padded_mut::<Pkcs7>(&mut buffer, data.len())
            .map_err(|e| AppError::EncryptionError(format!("Encryption failed: {}", e)))?;

        // 返回 IV + 加密数据的十六进制字符串
        let iv_hex = hex::encode(iv);
        let encrypted_hex = hex::encode(ciphertext);

        Ok(format!("{}{}", iv_hex, encrypted_hex))
    }

    /// 解密数据
    /// 输入格式：IV(32位十六进制) + 加密数据(十六进制)
    pub fn decrypt(&self, encrypted_hex: &str) -> Result<String, AppError> {
        if encrypted_hex.len() < 32 {
            return Err(AppError::DecryptionError("Data too short".to_string()));
        }

        // 提取IV和加密数据
        let iv_hex = &encrypted_hex[..32];
        let encrypted_data_hex = &encrypted_hex[32..];

        let iv_bytes = hex::decode(iv_hex)
            .map_err(|_| AppError::DecryptionError("Invalid IV format".to_string()))?;

        let mut encrypted_data = hex::decode(encrypted_data_hex)
            .map_err(|_| AppError::DecryptionError("Invalid encrypted data format".to_string()))?;

        if iv_bytes.len() != 16 {
            return Err(AppError::DecryptionError("Invalid IV length".to_string()));
        }

        // 转换IV为数组
        let mut iv = [0u8; 16];
        iv.copy_from_slice(&iv_bytes);

        // 创建解密器
        let cipher = Aes256CbcDec::new(&self.key.into(), &iv.into());

        // 解密数据
        let decrypted = cipher.decrypt_padded_mut::<Pkcs7>(&mut encrypted_data)
            .map_err(|e| AppError::DecryptionError(format!("Decryption failed: {}", e)))?;

        // 转换为字符串
        String::from_utf8(decrypted.to_vec())
            .map_err(|_| AppError::DecryptionError("Invalid UTF-8 in decrypted data".to_string()))
    }

    /// 检查数据是否为加密格式
    pub fn is_encrypted_format(data: &str) -> bool {
        // 检查是否为十六进制且长度足够
        data.len() > 32 && data.chars().all(|c| c.is_ascii_hexdigit())
    }
}

/// 解析登录响应
pub fn parse_login_response(response: &str) -> Result<(bool, Option<String>, String), AppError> {
    let response = response.trim();

    if response.starts_with("LOGIN_OK:") {
        // 解析成功响应: LOGIN_OK: Key="a1b2c3d4...";
        if let Some(key_start) = response.find("Key=\"") {
            let key_start = key_start + 5; // 跳过 Key="
            if let Some(key_end) = response[key_start..].find('"') {
                let key = response[key_start..key_start + key_end].to_string();
                return Ok((true, Some(key), "Login successful".to_string()));
            }
        }
        Err(AppError::AuthenticationError("Invalid login response format".to_string()))
    } else if response.starts_with("LOGIN_FAILED:") {
        // 解析失败响应: LOGIN_FAILED: Reason="Invalid credentials";
        let reason = if let Some(reason_start) = response.find("Reason=\"") {
            let reason_start = reason_start + 8; // 跳过 Reason="
            if let Some(reason_end) = response[reason_start..].find('"') {
                response[reason_start..reason_start + reason_end].to_string()
            } else {
                "Login failed".to_string()
            }
        } else {
            "Login failed".to_string()
        };
        Ok((false, None, reason))
    } else {
        Err(AppError::AuthenticationError("Unknown login response format".to_string()))
    }
}

/// 创建登录请求
pub fn create_login_request(username: &str, password: &str) -> String {
    format!("LOGIN: Username=\"{}\", Password=\"{}\";", username, password)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encryption_decryption() {
        let key_hex = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef";
        let encryption = EncryptionManager::new(key_hex).unwrap();

        let plaintext = "Hello, World!";
        let encrypted = encryption.encrypt(plaintext).unwrap();
        let decrypted = encryption.decrypt(&encrypted).unwrap();

        assert_eq!(plaintext, decrypted);
    }

    #[test]
    fn test_login_response_parsing() {
        let success_response = "LOGIN_OK: Key=\"abcd1234efgh5678\";";
        let (success, key, message) = parse_login_response(success_response).unwrap();

        assert!(success);
        assert_eq!(key, Some("abcd1234efgh5678".to_string()));
        assert_eq!(message, "Login successful");

        let fail_response = "LOGIN_FAILED: Reason=\"Invalid credentials\";";
        let (success, key, message) = parse_login_response(fail_response).unwrap();

        assert!(!success);
        assert_eq!(key, None);
        assert_eq!(message, "Invalid credentials");

        // 测试用户不存在的情况
        let user_not_found_response = "LOGIN_FAILED: Reason=\"User not found\";";
        let (success, key, message) = parse_login_response(user_not_found_response).unwrap();

        assert!(!success);
        assert_eq!(key, None);
        assert_eq!(message, "User not found");

        // 测试密码错误的情况
        let invalid_password_response = "LOGIN_FAILED: Reason=\"Invalid password\";";
        let (success, key, message) = parse_login_response(invalid_password_response).unwrap();

        assert!(!success);
        assert_eq!(key, None);
        assert_eq!(message, "Invalid password");
    }

    #[test]
    fn test_login_request_creation() {
        let request = create_login_request("admin", "123456");
        assert_eq!(request, "LOGIN: Username=\"admin\", Password=\"123456\";");
    }

    #[test]
    fn test_reproduce_json_error() {
        // 尝试重现 "Invalid character 'L' at position 0" 错误
        let test_strings = vec![
            "LOGIN_FAILED: Reason=\"User not found\";",
            "LOGIN_OK: Key=\"abcd1234\";",
            "User not found",
            "Invalid password",
            // 测试可能导致问题的情况
            "LOGIN_FAILED: Reason=\"User not found\";\n",
            "LOGIN_FAILED: Reason=\"User not found\"; ",
            "\nLOGIN_FAILED: Reason=\"User not found\";",
            " LOGIN_FAILED: Reason=\"User not found\";",
        ];

        for test_str in test_strings {
            println!("测试字符串: {:?}", test_str);

            // 测试原始字符串
            let json_result = serde_json::from_str::<serde_json::Value>(test_str);
            if let Err(e) = json_result {
                println!("JSON错误: {}", e);
                let error_str = e.to_string();
                if error_str.contains("Invalid character") && error_str.contains("position 0") {
                    println!("找到目标错误！");
                }
            }

            // 测试trim后的字符串
            let trimmed = test_str.trim();
            println!("Trim后: {:?}", trimmed);
            let json_result2 = serde_json::from_str::<serde_json::Value>(trimmed);
            if let Err(e) = json_result2 {
                println!("Trim后JSON错误: {}", e);
            }
        }

        // 尝试使用JavaScript风格的JSON解析来重现错误
        // 注意：这个测试可能不会产生完全相同的错误消息，因为Rust和JavaScript的JSON解析器不同
        println!("\n=== 尝试重现JavaScript风格的JSON解析错误 ===");
        let js_style_tests = vec![
            "LOGIN_FAILED: Reason=\"User not found\";",
            "LOGIN_OK: Key=\"abcd1234\";",
        ];

        for test_str in js_style_tests {
            println!("JS风格测试: {:?}", test_str);
            // 在Rust中，我们无法完全重现JavaScript的JSON.parse错误
            // 但我们可以检查字符串的第一个字符
            if let Some(first_char) = test_str.chars().next() {
                println!("第一个字符: '{}' (ASCII: {})", first_char, first_char as u32);
                if first_char == 'L' {
                    println!("发现以'L'开头的字符串，这可能导致JavaScript中的'Invalid character 'L' at position 0'错误");
                }
            }
        }
    }

    #[test]
    fn test_error_message_not_json() {
        // 测试确保错误消息不会被当作JSON解析
        let responses = vec![
            "LOGIN_FAILED: Reason=\"User not found\";",
            "LOGIN_FAILED: Reason=\"Invalid password\";",
            "LOGIN_FAILED: Reason=\"Invalid credentials\";",
        ];

        for response in responses {
            let result = parse_login_response(response);
            assert!(result.is_ok(), "Failed to parse response: {}", response);

            let (success, key, message) = result.unwrap();
            assert!(!success);
            assert_eq!(key, None);

            // 确保消息不包含JSON特殊字符
            assert!(!message.starts_with("{"));
            assert!(!message.starts_with("["));
            assert!(!message.contains("\""));

            // 测试如果有人试图将这个消息当作JSON解析会发生什么
            let json_parse_result = serde_json::from_str::<serde_json::Value>(&message);
            if json_parse_result.is_err() {
                let error = json_parse_result.unwrap_err();
                println!("JSON解析错误 (这是预期的): {}", error);
            }

            // 测试如果有人试图将原始响应当作JSON解析会发生什么
            let json_parse_result2 = serde_json::from_str::<serde_json::Value>(response);
            if json_parse_result2.is_err() {
                let error = json_parse_result2.unwrap_err();
                println!("原始响应JSON解析错误: {}", error);
                // 检查是否是 "Invalid character 'L' at position 0" 这样的错误
                let error_str = error.to_string();
                if error_str.contains("Invalid character") && error_str.contains("at position 0") {
                    println!("找到了问题根源！原始响应解析错误: {}", error_str);
                }
            }
        }
    }
}