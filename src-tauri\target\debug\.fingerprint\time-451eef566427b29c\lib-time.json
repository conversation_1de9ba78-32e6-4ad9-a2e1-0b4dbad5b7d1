{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\", \"formatting\", \"local-offset\", \"macros\", \"parsing\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 16238415492269843011, "path": 2826658645577454002, "deps": [[724804171976944018, "num_conv", false, 9056041338518977999], [4880290578780516359, "num_threads", false, 1199962876332466540], [5781077862495881698, "libc", false, 13330442044175659524], [5901133744777009488, "powerfmt", false, 11985133481899572493], [9272993696668697301, "itoa", false, 11377687992711802143], [9963315149517826755, "deranged", false, 907969759005738858], [11984954412579621937, "time_macros", false, 1658593314425255041], [14288011715199747663, "time_core", false, 12266744449368921147]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/time-451eef566427b29c/dep-lib-time", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}