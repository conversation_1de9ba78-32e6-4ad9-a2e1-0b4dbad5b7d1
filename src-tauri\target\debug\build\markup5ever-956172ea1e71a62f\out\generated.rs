pub type LocalName = :: string_cache :: Atom < LocalNameStaticSet > ;
# [derive (PartialEq , Eq , PartialOrd , Ord)] pub struct LocalNameStaticSet ;
impl :: string_cache :: StaticAtomSet for LocalNameStaticSet { fn get () -> & 'static :: string_cache :: PhfStrSet { static SET : :: string_cache :: PhfStrSet = :: string_cache :: PhfStrSet { key : 12913932095322966823u64 , disps : & [(0u32 , 142u32) , (0u32 , 112u32) , (0u32 , 2u32) , (0u32 , 114u32) , (0u32 , 149u32) , (0u32 , 1u32) , (0u32 , 207u32) , (0u32 , 615u32) , (0u32 , 2u32) , (0u32 , 60u32) , (0u32 , 16u32) , (0u32 , 17u32) , (0u32 , 168u32) , (0u32 , 3u32) , (1u32 , 52u32) , (0u32 , 3u32) , (0u32 , 12u32) , (0u32 , 68u32) , (0u32 , 130u32) , (0u32 , 807u32) , (0u32 , 2u32) , (0u32 , 404u32) , (0u32 , 145u32) , (0u32 , 396u32) , (0u32 , 455u32) , (0u32 , 0u32) , (0u32 , 60u32) , (0u32 , 0u32) , (0u32 , 0u32) , (0u32 , 2u32) , (0u32 , 199u32) , (0u32 , 915u32) , (0u32 , 348u32) , (1u32 , 137u32) , (0u32 , 260u32) , (0u32 , 986u32) , (0u32 , 91u32) , (0u32 , 18u32) , (0u32 , 54u32) , (0u32 , 10u32) , (0u32 , 438u32) , (0u32 , 96u32) , (0u32 , 274u32) , (0u32 , 182u32) , (1u32 , 9u32) , (0u32 , 1u32) , (0u32 , 2u32) , (0u32 , 0u32) , (0u32 , 212u32) , (0u32 , 4u32) , (0u32 , 33u32) , (2u32 , 814u32) , (0u32 , 8u32) , (0u32 , 327u32) , (0u32 , 14u32) , (0u32 , 7u32) , (0u32 , 1062u32) , (0u32 , 647u32) , (0u32 , 11u32) , (0u32 , 382u32) , (1u32 , 621u32) , (4u32 , 1091u32) , (0u32 , 0u32) , (0u32 , 17u32) , (0u32 , 592u32) , (0u32 , 247u32) , (0u32 , 94u32) , (0u32 , 375u32) , (0u32 , 6u32) , (0u32 , 223u32) , (0u32 , 39u32) , (0u32 , 208u32) , (1u32 , 373u32) , (0u32 , 46u32) , (2u32 , 596u32) , (0u32 , 0u32) , (0u32 , 25u32) , (0u32 , 41u32) , (0u32 , 295u32) , (0u32 , 177u32) , (0u32 , 10u32) , (1u32 , 729u32) , (0u32 , 48u32) , (2u32 , 69u32) , (0u32 , 8u32) , (0u32 , 34u32) , (0u32 , 12u32) , (0u32 , 741u32) , (0u32 , 1u32) , (0u32 , 726u32) , (0u32 , 0u32) , (0u32 , 21u32) , (0u32 , 0u32) , (0u32 , 4u32) , (0u32 , 3u32) , (0u32 , 747u32) , (0u32 , 2u32) , (0u32 , 364u32) , (0u32 , 99u32) , (0u32 , 77u32) , (0u32 , 34u32) , (0u32 , 313u32) , (0u32 , 0u32) , (0u32 , 54u32) , (0u32 , 11u32) , (1u32 , 0u32) , (0u32 , 80u32) , (0u32 , 113u32) , (0u32 , 537u32) , (0u32 , 5u32) , (0u32 , 681u32) , (0u32 , 3u32) , (0u32 , 21u32) , (0u32 , 1u32) , (0u32 , 328u32) , (0u32 , 326u32) , (0u32 , 2u32) , (0u32 , 69u32) , (0u32 , 6u32) , (0u32 , 158u32) , (0u32 , 0u32) , (0u32 , 2u32) , (0u32 , 0u32) , (0u32 , 118u32) , (0u32 , 7u32) , (0u32 , 23u32) , (0u32 , 873u32) , (0u32 , 556u32) , (0u32 , 13u32) , (0u32 , 9u32) , (0u32 , 7u32) , (0u32 , 115u32) , (0u32 , 44u32) , (0u32 , 1u32) , (0u32 , 599u32) , (0u32 , 2u32) , (0u32 , 124u32) , (0u32 , 973u32) , (0u32 , 28u32) , (0u32 , 549u32) , (0u32 , 11u32) , (0u32 , 111u32) , (1u32 , 966u32) , (0u32 , 517u32) , (0u32 , 44u32) , (0u32 , 238u32) , (1u32 , 287u32) , (0u32 , 61u32) , (7u32 , 758u32) , (0u32 , 83u32) , (0u32 , 347u32) , (0u32 , 1u32) , (0u32 , 66u32) , (1u32 , 198u32) , (0u32 , 34u32) , (0u32 , 25u32) , (0u32 , 185u32) , (0u32 , 310u32) , (0u32 , 4u32) , (0u32 , 344u32) , (4u32 , 538u32) , (0u32 , 62u32) , (0u32 , 8u32) , (0u32 , 481u32) , (0u32 , 85u32) , (0u32 , 0u32) , (0u32 , 2u32) , (0u32 , 83u32) , (0u32 , 15u32) , (9u32 , 762u32) , (1u32 , 329u32) , (0u32 , 8u32) , (0u32 , 236u32) , (0u32 , 208u32) , (0u32 , 1053u32) , (0u32 , 10u32) , (0u32 , 944u32) , (0u32 , 620u32) , (0u32 , 112u32) , (0u32 , 4u32) , (0u32 , 166u32) , (0u32 , 643u32) , (1u32 , 962u32) , (0u32 , 72u32) , (0u32 , 24u32) , (0u32 , 46u32) , (0u32 , 85u32) , (0u32 , 218u32) , (11u32 , 212u32) , (1u32 , 570u32) , (0u32 , 624u32) , (1u32 , 822u32) , (0u32 , 104u32) , (3u32 , 368u32) , (3u32 , 332u32) , (0u32 , 0u32) , (0u32 , 324u32) , (0u32 , 828u32) , (0u32 , 35u32) , (0u32 , 8u32) , (0u32 , 29u32) , (0u32 , 154u32) , (0u32 , 404u32) , (7u32 , 897u32) , (0u32 , 0u32) , (0u32 , 882u32) , (0u32 , 1u32) , (0u32 , 478u32) , (0u32 , 2u32) , (0u32 , 265u32) , (0u32 , 322u32) , (0u32 , 0u32) , (1u32 , 943u32) , (5u32 , 512u32) , (21u32 , 767u32) , (0u32 , 61u32) , (0u32 , 13u32) , (2u32 , 853u32) , (50u32 , 460u32) , (0u32 , 157u32) , (18u32 , 223u32) , (0u32 , 702u32)] , atoms : & ["dur",
"units-per-em",
"selected",
"patternTransform",
"th",
"and",
"aria-secret",
"mglyph",
"grad",
"select",
"lowsrc",
"line-height",
"geq",
"ping",
"contentStyleType",
"aria-checked",
"ln",
"dataformatas",
"ondrag",
"declare",
"onerror",
"p",
"msup",
"kbd",
"marginheight",
"svg",
"filterRes",
"wrap",
"h1",
"noembed",
"displaystyle",
"numoctaves",
"address",
"async",
"ychannelselector",
"horiz-origin-x",
"aside",
"fecomposite",
"onblur",
"diffuseconstant",
"leq",
"list",
"axis",
"codomain",
"product",
"verythickmathspace",
"zoomandpan",
"fespotlight",
"values",
"rspace",
"marker-mid",
"dirname",
"scrolldelay",
"onload",
"datasrc",
"replace",
"formmethod",
"preserveAlpha",
"flood-color",
"minsize",
"femergenode",
"cy",
"feSpecularLighting",
"formnovalidate",
"stop-opacity",
"z",
"munder",
"slope",
"csymbol",
"index",
"union",
"slot",
"missing-glyph",
"x-height",
"ondblclick",
"limitingConeAngle",
"approx",
"x",
"imaginaryi",
"pointer-events",
"font-weight",
"specularConstant",
"in2",
"onfocusout",
"step",
"wbr",
"glyph-orientation-vertical",
"feBlend",
"scriptsizemultiplier",
"hspace",
"columnspan",
"progress",
"autocomplete",
"rule",
"ceiling",
"aria-owns",
"intercept",
"space",
"nextid",
"feMorphology",
"cellspacing",
"start",
"calcMode",
"rem",
"legend",
"onstart",
"path",
"feFuncG",
"onafterprint",
"primitiveUnits",
"altimg",
"refX",
"viewBox",
"columnlines",
"animateColor",
"patternUnits",
"optimum",
"onbounce",
"lengthAdjust",
"srclang",
"alttext",
"mathbackground",
"aria-required",
"ellipse",
"iframe",
"altGlyphItem",
"v-hanging",
"lang",
"fecolormatrix",
"alignment-baseline",
"mi",
"tt",
"xlink",
"macros",
"for",
"onmousewheel",
"script",
"tbreak",
"overflow",
"underline-position",
"stddeviation",
"prefetch",
"dl",
"char",
"text-anchor",
"nav",
"scheme",
"bgsound",
"unicode-range",
"markerHeight",
"br",
"none",
"attributetype",
"patterncontentunits",
"tfoot",
"onrowenter",
"crossorigin",
"textLength",
"fedistantlight",
"onchange",
"oncut",
"headers",
"use",
"int",
"fontstyle",
"rowspan",
"switch",
"stroke-opacity",
"domain",
"xlink:href",
"ondragstart",
"xlink:actuate",
"ms",
"oncopy",
"onscroll",
"altglyphitem",
"primitiveunits",
"bvar",
"mprescripts",
"center",
"min",
"fontweight",
"xmp",
"equalcolumns",
"link",
"aria-valuemax",
"minlength",
"selector",
"noresize",
"inverse",
"maxlength",
"partialdiff",
"reals",
"femerge",
"domainofapplication",
"xml:base",
"mfrac",
"g2",
"oninput",
"matrix",
"surfacescale",
"piece",
"power",
"input",
"q",
"repeat-template",
"table",
"feconvolvematrix",
"apply",
"clippath",
"http-equiv",
"discard",
"complexes",
"marker",
"markerWidth",
"listing",
"gradientUnits",
"aria-valuemin",
"repeatcount",
"track",
"groupalign",
"feDistantLight",
"maskContentUnits",
"arctanh",
"csch",
"onbeforeactivate",
"onreadystatechange",
"ononline",
"rowspacing",
"onbeforecut",
"aria-labelledby",
"superscriptshift",
"ondatasetcomplete",
"embed",
"elevation",
"arctan",
"letter-spacing",
"aria-sort",
"mtd",
"keysplines",
"onfinish",
"feComponentTransfer",
"autoplay",
"panose-1",
"h5",
"scope",
"determinant",
"kernelUnitLength",
"x1",
"aria-invalid",
"handler",
"neq",
"arccsch",
"mask",
"output",
"usemap",
"preserveAspectRatio",
"xlink:role",
"feoffset",
"xref",
"keygen",
"onrowexit",
"string",
"animatetransform",
"v-ideographic",
"manifest",
"degree",
"ci",
"mstyle",
"abbr",
"fefuncb",
"h2",
"logbase",
"alink",
"markerheight",
"form",
"altglyphdef",
"display",
"orient",
"clip",
"accept",
"arabic-form",
"onlosecapture",
"textpath",
"mtr",
"xml:space",
"y2",
"menu",
"picture",
"codebase",
"targetx",
"rationals",
"eq",
"set",
"aria-multiselectable",
"symmetric",
"stroke-linejoin",
"minus",
"thickmathspace",
"subscriptshift",
"lspace",
"piecewise",
"median",
"externalresourcesrequired",
"ondragend",
"radius",
"refY",
"seamless",
"version",
"em",
"contentstyletype",
"line",
"columnspacing",
"onmousemove",
"divergence",
"onpageshow",
"tan",
"repeatdur",
"spellcheck",
"feFlood",
"feFuncR",
"font-family",
"onafterupdate",
"cx",
"scriptlevel",
"colspan",
"isindex",
"src",
"aria-selected",
"kernelmatrix",
"onmoveend",
"aria-dropeffect",
"fegaussianblur",
"stitchtiles",
"required",
"ondragdrop",
"pathlength",
"onbegin",
"nohref",
"horiz-adv-x",
"oncellchange",
"vlink",
"curl",
"figure",
"preserveaspectratio",
"statechange",
"cosh",
"mlabeledtr",
"subset",
"color-rendering",
"surfaceScale",
"onbeforeupdate",
"formaction",
"linearGradient",
"kind",
"transform",
"rx",
"aria-describedby",
"arg",
"spreadMethod",
"profile",
"cartesianproduct",
"maskcontentunits",
"clip-rule",
"reversed",
"font-variant",
"diffuseConstant",
"code",
"figcaption",
"bevelled",
"mathsize",
"tableValues",
"color",
"mtext",
"vkern",
"close",
"body",
"font-face-src",
"repeatDur",
"keyTimes",
"s",
"value",
"munderover",
"feColorMatrix",
"animation",
"columnalign",
"strike",
"mo",
"",
"aria-multiline",
"root",
"font-style",
"msubsup",
"repeat",
"textPath",
"mathematical",
"baseProfile",
"actuate",
"type",
"color-interpolation",
"cursor",
"font-stretch",
"data",
"gradienttransform",
"rquote",
"unicode-bidi",
"aria-level",
"onpagehide",
"stretchy",
"dialog",
"end",
"font-face-uri",
"coth",
"animatecolor",
"pathLength",
"option",
"calcmode",
"itemtype",
"math",
"tabindex",
"marker-end",
"repeat-max",
"bbox",
"cos",
"fontsize",
"definition-src",
"fy",
"feimage",
"menuitem",
"checked",
"v-alphabetic",
"refy",
"accent",
"onfocusin",
"glyph-name",
"sup",
"bdi",
"arccos",
"condition",
"interval",
"other",
"semantics",
"visibility",
"aria-haspopup",
"aria-channel",
"feFuncB",
"repeatCount",
"object",
"g1",
"pi",
"pointsatz",
"hidden",
"basefrequency",
"id",
"startoffset",
"del",
"when",
"maskunits",
"exponentiale",
"muted",
"annotation",
"filter",
"thead",
"tr",
"autofocus",
"limitingconeangle",
"xlink:show",
"itemscope",
"polyline",
"main",
"sizes",
"animate",
"dy",
"maction",
"loop",
"onmessage",
"edgeMode",
"arccoth",
"fill-rule",
"contentScriptType",
"exists",
"gt",
"closure",
"datatemplate",
"tanh",
"clippathunits",
"preload",
"controls",
"href",
"specularExponent",
"itemref",
"xlink:type",
"lowlimit",
"defs",
"ul",
"vert-origin-x",
"lighting-color",
"arcsec",
"desc",
"origin",
"veryverythickmathspace",
"rows",
"repeat-min",
"high",
"format",
"notsubset",
"scale",
"arcsinh",
"arcrole",
"mrow",
"image-rendering",
"onfilterchange",
"feSpotLight",
"font-face",
"specularconstant",
"largeop",
"fill",
"onmove",
"ol",
"source",
"onabort",
"aria-controls",
"xchannelselector",
"symbol",
"quotient",
"lineargradient",
"rb",
"toggle",
"color-interpolation-filters",
"default",
"accesskey",
"font-size",
"mphantom",
"tablevalues",
"stemh",
"onbefordeactivate",
"onunload",
"feFuncA",
"sec",
"viewTarget",
"view",
"srcset",
"poster",
"audio",
"orientation",
"vector",
"amplitude",
"a",
"rules",
"placeholder",
"clip-path",
"feGaussianBlur",
"conjugate",
"polygon",
"mroot",
"arcsech",
"maligngroup",
"in",
"color-profile",
"standby",
"ry",
"span",
"nobr",
"specularexponent",
"accentunder",
"aria-disabled",
"ruby",
"dd",
"imaginary",
"area",
"verythinmathspace",
"fence",
"patternContentUnits",
"order",
"exp",
"onbeforeeditfocus",
"enctype",
"sep",
"requiredfeatures",
"animateMotion",
"r",
"itemid",
"xlink:arcrole",
"fedropshadow",
"fetch",
"text-decoration",
"onfocus",
"vert-adv-y",
"formtarget",
"blink",
"scoped",
"moment",
"method",
"filterUnits",
"mean",
"lcm",
"strong",
"equalrows",
"onselectstart",
"local",
"feDropShadow",
"onmousedown",
"gcd",
"multicol",
"h6",
"stop",
"ondrop",
"arccsc",
"onzoom",
"clear",
"sub",
"itemprop",
"scalarproduct",
"onsubmit",
"feDisplacementMap",
"feTurbulence",
"outerproduct",
"allowfullscreen",
"label",
"sdev",
"exponent",
"speed",
"font-face-name",
"descent",
"font",
"applet",
"feturbulence",
"fontfamily",
"pattern",
"article",
"active",
"referrerpolicy",
"refx",
"vectorproduct",
"card",
"rotate",
"oncontrolselect",
"maskUnits",
"rev",
"fefunca",
"rendering-intent",
"unicode",
"ismap",
"onreset",
"samp",
"emptyset",
"ondragenter",
"event",
"lquote",
"html",
"columnwidth",
"opacity",
"decoding",
"nest",
"offset",
"systemLanguage",
"cols",
"naturalnumbers",
"mtable",
"valuetype",
"target",
"maxsize",
"noscript",
"aria-posinset",
"style",
"inputmode",
"aria-grab",
"writing-mode",
"aria-datatype",
"frame",
"dx",
"x2",
"real",
"stroke",
"onpaste",
"mpath",
"mediummathspace",
"ident",
"datetime",
"div",
"eulergamma",
"csc",
"k",
"false",
"accent-height",
"hreflang",
"k2",
"viewtarget",
"gradientTransform",
"separator",
"defer",
"from",
"datafld",
"glyph-orientation-horizontal",
"numOctaves",
"mover",
"nowrap",
"radialGradient",
"notin",
"font-size-adjust",
"cellpadding",
"markerUnits",
"dt",
"floor",
"stop-color",
"footer",
"selection",
"stroke-dashoffset",
"property",
"primes",
"onstorage",
"onmouseup",
"media",
"summary",
"mode",
"nargs",
"novalidate",
"integrity",
"dominant-baseline",
"mathcolor",
"icon",
"open",
"aria-autocomplete",
"optgroup",
"dfn",
"acronym",
"align",
"radiogroup",
"tref",
"aria-pressed",
"true",
"hkern",
"integers",
"aria-flowto",
"rel",
"contenteditable",
"rt",
"aria-expanded",
"tspan",
"fedisplacementmap",
"k4",
"to",
"foreignobject",
"parse",
"definitionURL",
"menclose",
"onmouseenter",
"archive",
"hr",
"compose",
"fill-opacity",
"mn",
"sech",
"additive",
"msub",
"horiz-origin-y",
"border",
"bias",
"font-face-format",
"accept-charset",
"framespacing",
"fepointlight",
"requiredFeatures",
"clipPath",
"feComposite",
"animatemotion",
"lt",
"onbeforeunload",
"altGlyph",
"role",
"feMerge",
"edge",
"onforminput",
"pointsAtY",
"li",
"vspace",
"multiple",
"merror",
"aria-activedescendant",
"onhashchange",
"prsubset",
"baseline",
"td",
"rp",
"factorof",
"oncontextmenu",
"low",
"draggable",
"onpopstate",
"occurrence",
"time",
"targetY",
"width",
"fieldset",
"startOffset",
"otherwise",
"charoff",
"language",
"limit",
"plaintext",
"ondragover",
"shape-rendering",
"blockquote",
"feblend",
"onkeydown",
"image",
"fn",
"nomodule",
"xmlns:xlink",
"noframes",
"altGlyphDef",
"irrelevant",
"nonce",
"background",
"forall",
"xmlns",
"result",
"stroke-dasharray",
"abs",
"details",
"translate",
"equivalent",
"factorial",
"prompt",
"mathvariant",
"times",
"requiredExtensions",
"stitchTiles",
"uplimit",
"fefuncg",
"spreadmethod",
"onrepeat",
"onoffline",
"autosubmit",
"log",
"spacer",
"cap-height",
"kernelunitlength",
"onlanguagechange",
"coords",
"infinity",
"bgcolor",
"codetype",
"keytimes",
"onbeforeprint",
"overline-position",
"implies",
"onrowsinserted",
"contentscripttype",
"head",
"valign",
"name",
"animateTransform",
"pointsatx",
"laplacian",
"metadata",
"actiontype",
"pointsAtZ",
"class",
"widths",
"download",
"onmouseout",
"u1",
"baseprofile",
"ondataavailable",
"y",
"unselectable",
"or",
"mspace",
"alphabetic",
"targety",
"filterunits",
"feMergeNode",
"systemlanguage",
"onkeypress",
"d",
"frameborder",
"fePointLight",
"sum",
"onbeforecopy",
"onresize",
"yChannelSelector",
"sinh",
"i",
"ondatasetchanged",
"targetX",
"variance",
"stemv",
"big",
"sin",
"filterres",
"mozbrowser",
"ideographic",
"onhelp",
"action",
"longdesc",
"fx",
"textarea",
"intersect",
"specification",
"veryverythinmathspace",
"strikethrough-thickness",
"sandbox",
"oninvalid",
"foreignObject",
"aria-busy",
"fetile",
"flood-opacity",
"map",
"text-rendering",
"onmovestart",
"circle",
"button",
"stroke-miterlimit",
"baseline-shift",
"notanumber",
"b",
"feConvolveMatrix",
"kerning",
"keyPoints",
"k3",
"contextmenu",
"aria-setsize",
"transpose",
"listener",
"dir",
"edgemode",
"face",
"begin",
"controllerchange",
"onmouseover",
"meta",
"malignmark",
"k1",
"meter",
"plus",
"arccot",
"mfenced",
"altglyph",
"aria-templateid",
"xChannelSelector",
"definitionurl",
"text",
"srcdoc",
"onformchange",
"baseFrequency",
"xlink:title",
"max",
"rtc",
"tendsto",
"alignmentscope",
"ins",
"title",
"onpropertychange",
"encoding",
"notprsubset",
"operator",
"underline-thickness",
"onbeforepaste",
"fecomponenttransfer",
"height",
"solidcolor",
"femorphology",
"vert-origin-y",
"notation",
"template",
"pre",
"h3",
"repeat-start",
"seed",
"glyphref",
"onerrorupdate",
"fespecularlighting",
"feflood",
"*",
"canvas",
"onselect",
"pointsAtX",
"direction",
"matrixrow",
"formenctype",
"markerunits",
"header",
"onkeyup",
"lengthadjust",
"mark",
"cn",
"aria-valuenow",
"markerwidth",
"ascent",
"show",
"param",
"pointsaty",
"msqrt",
"readonly",
"feImage",
"patternunits",
"spacing",
"word-spacing",
"marquee",
"colgroup",
"onactivate",
"stroke-width",
"annotation-xml",
"aria-hidden",
"requiredextensions",
"charset",
"movablelimits",
"stroke-linecap",
"enable-background",
"onstop",
"size",
"momentabout",
"g",
"basefont",
"u",
"not",
"h4",
"linethickness",
"aria-relevant",
"linebreak",
"onmouseleave",
"xml:lang",
"overline-thickness",
"xor",
"rect",
"mmultiscripts",
"onrowsdelete",
"bdo",
"accumulate",
"section",
"divide",
"img",
"lambda",
"gradientunits",
"preservealpha",
"small",
"frameset",
"var",
"content",
"attributeName",
"onend",
"thinmathspace",
"viewbox",
"marker-start",
"glyphRef",
"attributename",
"alt",
"cite",
"rowalign",
"externalResourcesRequired",
"feDiffuseLighting",
"video",
"rowlines",
"stdDeviation",
"compact",
"hidefocus",
"hanging",
"separators",
"scriptminsize",
"base",
"col",
"points",
"tbody",
"onclick",
"fediffuselighting",
"aria-atomic",
"strikethrough-position",
"textlength",
"aria-live",
"azimuth",
"radialgradient",
"feTile",
"keySplines",
"classid",
"restart",
"ondeactivate",
"clipPathUnits",
"reln",
"arcsin",
"disabled",
"caption",
"patterntransform",
"aria-readonly",
"shape",
"noshade",
"v-mathematical",
"mpadded",
"depth",
"feOffset",
"keypoints",
"y1",
"fefuncr",
"divisor",
"marginwidth",
"hgroup",
"arccosh",
"cot",
"datalist",
"glyph",
"kernelMatrix",
"attributeType",
"setdiff",
"u2",
"ondragleave",
"scrolling",
"zoomAndPan",
"by",
"diff"] , hashes : & [2234510680u32 , 2673802232u32 , 1326112048u32 , 2555432291u32 , 3030471804u32 , 1150843912u32 , 3391050331u32 , 3666749829u32 , 1948197221u32 , 1325939503u32 , 414121004u32 , 1029483847u32 , 3138542682u32 , 3012156523u32 , 1496007974u32 , 2710653095u32 , 4079840542u32 , 3538034810u32 , 2166163946u32 , 3712274519u32 , 1493772713u32 , 3868900413u32 , 207735385u32 , 4232762703u32 , 1341662051u32 , 3902001267u32 , 23332298u32 , 1000698605u32 , 1058837628u32 , 42384297u32 , 1693167880u32 , 3505831613u32 , 1760739777u32 , 4061972332u32 , 1079146945u32 , 960352296u32 , 185884973u32 , 2660037162u32 , 2467431560u32 , 2909783456u32 , 2295989784u32 , 2778544105u32 , 3040373773u32 , 3848540887u32 , 447734275u32 , 4274065971u32 , 3653391143u32 , 1469020628u32 , 1834101858u32 , 2877167735u32 , 3503166008u32 , 1146457377u32 , 3588373335u32 , 3658418485u32 , 2509793760u32 , 3667422931u32 , 2901051737u32 , 1054325476u32 , 2068298677u32 , 123800926u32 , 2229477384u32 , 1697590725u32 , 2875176293u32 , 3572217425u32 , 2377326793u32 , 1849672485u32 , 4252201132u32 , 1181865717u32 , 2397015458u32 , 2983961815u32 , 418618809u32 , 355519382u32 , 4214847380u32 , 67060975u32 , 3318082606u32 , 175932733u32 , 4133028771u32 , 2756908117u32 , 3225919750u32 , 570359024u32 , 2754786514u32 , 1503507106u32 , 1587898242u32 , 4266430317u32 , 1844361094u32 , 2362637345u32 , 3720179864u32 , 1536632361u32 , 1106010321u32 , 2250472488u32 , 1113648994u32 , 3164895664u32 , 139625561u32 , 1754115804u32 , 903602157u32 , 1528320406u32 , 2250640510u32 , 1224089u32 , 2461483617u32 , 1969026116u32 , 3087731897u32 , 4234761996u32 , 1713731671u32 , 3929006940u32 , 171222834u32 , 3710863668u32 , 1380239101u32 , 4199665453u32 , 3088760233u32 , 235814134u32 , 1968334064u32 , 849449597u32 , 3504125432u32 , 3139000449u32 , 2274933261u32 , 718491279u32 , 782184238u32 , 236464811u32 , 3766194097u32 , 3222412735u32 , 3666761738u32 , 41264406u32 , 303986147u32 , 1824216206u32 , 2570653005u32 , 1660726878u32 , 3574495545u32 , 1297307566u32 , 2627228424u32 , 3727352922u32 , 2637290003u32 , 2990700622u32 , 2646948180u32 , 2322716179u32 , 2835436835u32 , 825000363u32 , 2392965895u32 , 4194776985u32 , 4010765371u32 , 3161172129u32 , 2791371120u32 , 2884343567u32 , 4105594630u32 , 3802817635u32 , 1747256964u32 , 3421247560u32 , 2135316053u32 , 654777154u32 , 3054316836u32 , 3420533286u32 , 4014958776u32 , 4215833524u32 , 3447324541u32 , 2965230298u32 , 3038441148u32 , 1285071230u32 , 1099196523u32 , 3015830598u32 , 597485693u32 , 671909442u32 , 4263436362u32 , 51506945u32 , 1273749580u32 , 705165595u32 , 89533980u32 , 3278678123u32 , 3868980231u32 , 2007550408u32 , 2381415889u32 , 2985998690u32 , 2331857769u32 , 497920229u32 , 1025781044u32 , 2211055779u32 , 3263344947u32 , 1160497299u32 , 660920321u32 , 4089167428u32 , 4279947214u32 , 4267760977u32 , 683509362u32 , 1436742140u32 , 2271345682u32 , 3371793166u32 , 1102757337u32 , 2627759492u32 , 1114315443u32 , 1151125310u32 , 3709793219u32 , 3434645925u32 , 4155473355u32 , 3140418151u32 , 173634036u32 , 1850141989u32 , 1000927536u32 , 3107512368u32 , 2210310308u32 , 434586848u32 , 271329001u32 , 2052503918u32 , 4193230156u32 , 4061826754u32 , 1256699373u32 , 3790724572u32 , 494432567u32 , 1363498200u32 , 3250191289u32 , 1038372645u32 , 3864273530u32 , 1365633322u32 , 1465547969u32 , 3011660448u32 , 3761485389u32 , 3682934193u32 , 3535003966u32 , 1678664026u32 , 1089255496u32 , 1737183206u32 , 4144887102u32 , 655080190u32 , 3978774502u32 , 3022346510u32 , 2761967137u32 , 1947003845u32 , 630747693u32 , 1833541319u32 , 1081225797u32 , 1545307082u32 , 1152797465u32 , 2363338602u32 , 1696233065u32 , 4197022858u32 , 297387744u32 , 1702873090u32 , 3074935477u32 , 1638532578u32 , 4104071589u32 , 2172529172u32 , 3614904285u32 , 1980252733u32 , 1500515759u32 , 1003700867u32 , 2113798551u32 , 798432117u32 , 2125303890u32 , 3711536507u32 , 4114830095u32 , 3394829011u32 , 2690168509u32 , 3472575083u32 , 4204390246u32 , 1356591456u32 , 2003234786u32 , 2219898174u32 , 4242245856u32 , 2229560705u32 , 4080036470u32 , 2705478669u32 , 1281759160u32 , 3068760032u32 , 2191740635u32 , 130627326u32 , 3293186083u32 , 4088548386u32 , 143478574u32 , 2927217335u32 , 8379005u32 , 4250499820u32 , 2378024993u32 , 3181810604u32 , 2812859730u32 , 3039008911u32 , 3871587251u32 , 739780104u32 , 3367662520u32 , 2024968194u32 , 2282081120u32 , 34937617u32 , 2442913746u32 , 169227419u32 , 763259218u32 , 3199297686u32 , 3937191824u32 , 1290517656u32 , 2822084181u32 , 544785510u32 , 927236544u32 , 3495408325u32 , 2548012788u32 , 1918948260u32 , 3087218890u32 , 1659529969u32 , 4027115771u32 , 1386122418u32 , 882636649u32 , 3391938715u32 , 388019751u32 , 4284346275u32 , 2488889144u32 , 1324932700u32 , 788771646u32 , 1340310784u32 , 1498440529u32 , 1068196084u32 , 4019205149u32 , 3413889554u32 , 2049953055u32 , 1967061597u32 , 67570456u32 , 824143574u32 , 787973706u32 , 1020198921u32 , 3072954000u32 , 3598404451u32 , 3590479039u32 , 1784393862u32 , 431905749u32 , 3173625849u32 , 3345816294u32 , 278885378u32 , 4203201272u32 , 4121432320u32 , 1463711312u32 , 3963094042u32 , 261387879u32 , 1371589245u32 , 2797369290u32 , 3428477807u32 , 940673285u32 , 1511898050u32 , 722849851u32 , 1402964693u32 , 2606928828u32 , 1578024761u32 , 1014874794u32 , 4174481087u32 , 3041455211u32 , 13939510u32 , 3757867551u32 , 3480491085u32 , 3561280171u32 , 3230490522u32 , 2548120553u32 , 396757642u32 , 13967138u32 , 2075053822u32 , 67912785u32 , 167340041u32 , 1984356571u32 , 3517724257u32 , 3311581079u32 , 1064789251u32 , 3859396703u32 , 1956396338u32 , 2353997387u32 , 2990288183u32 , 2998184538u32 , 2730644329u32 , 157988637u32 , 150393700u32 , 2812786495u32 , 3650491062u32 , 3648607726u32 , 4179141959u32 , 3776643770u32 , 3735993810u32 , 898067958u32 , 262645782u32 , 2771320901u32 , 42180808u32 , 191876287u32 , 836037603u32 , 3374296737u32 , 777563392u32 , 4041034977u32 , 3111674091u32 , 662200295u32 , 3569972244u32 , 642136761u32 , 1135843586u32 , 1904744880u32 , 1928649000u32 , 1108361085u32 , 3819629636u32 , 1344436999u32 , 95564789u32 , 3433960873u32 , 3395089108u32 , 4082073077u32 , 688563368u32 , 4041222665u32 , 1109713652u32 , 4081904869u32 , 1095105424u32 , 2005060454u32 , 631162154u32 , 268294220u32 , 627157391u32 , 2340151391u32 , 2938551886u32 , 270981811u32 , 1060911337u32 , 2297035459u32 , 2468381339u32 , 894428246u32 , 4260186941u32 , 948133874u32 , 3730423347u32 , 4157184116u32 , 938891037u32 , 908933404u32 , 3038336232u32 , 1827361888u32 , 3862168567u32 , 2152634883u32 , 150823702u32 , 1924991364u32 , 876893636u32 , 342910504u32 , 441619653u32 , 1639866691u32 , 742460070u32 , 445054362u32 , 3919937075u32 , 2140727092u32 , 1425318046u32 , 1687848216u32 , 3347513700u32 , 3273539984u32 , 3969247273u32 , 2192643058u32 , 882588460u32 , 604024844u32 , 3349181976u32 , 3953335784u32 , 752636074u32 , 1368901689u32 , 311085664u32 , 1492357159u32 , 3813953811u32 , 415004145u32 , 189988860u32 , 680267915u32 , 2893479988u32 , 4257931290u32 , 2467217256u32 , 4004244404u32 , 4036843480u32 , 1114660064u32 , 4015812680u32 , 2980412961u32 , 1634534214u32 , 4219469015u32 , 937717349u32 , 3023844475u32 , 3808849337u32 , 1851109935u32 , 1008080924u32 , 527925402u32 , 3361694260u32 , 1960989138u32 , 1494323141u32 , 2752679045u32 , 3055049187u32 , 2488083091u32 , 4086049772u32 , 731340681u32 , 1437637129u32 , 2537656704u32 , 4262303727u32 , 1307082681u32 , 1804017132u32 , 3555644606u32 , 4199680039u32 , 2069591134u32 , 2526332624u32 , 427162403u32 , 300579694u32 , 183626286u32 , 4019313672u32 , 3742507012u32 , 1553568401u32 , 3271273745u32 , 1013909549u32 , 2279738660u32 , 2648768565u32 , 2577957964u32 , 623782337u32 , 4069948231u32 , 3353385752u32 , 3182856880u32 , 3629415669u32 , 2766318564u32 , 1038736949u32 , 2218757200u32 , 2286720981u32 , 3799256240u32 , 2679162678u32 , 491405495u32 , 3054955216u32 , 1964478094u32 , 1479082945u32 , 3231125223u32 , 3860308009u32 , 593929698u32 , 1123905875u32 , 1677013279u32 , 145077647u32 , 2933611753u32 , 281302065u32 , 3725445512u32 , 990264918u32 , 2701977759u32 , 59658625u32 , 4048787516u32 , 1956171144u32 , 1808534403u32 , 3307009014u32 , 3376838313u32 , 1860706754u32 , 2624297068u32 , 269731020u32 , 4064199297u32 , 349046089u32 , 890471348u32 , 797499465u32 , 3727156162u32 , 2634614319u32 , 840604679u32 , 2994561025u32 , 1340705914u32 , 938548574u32 , 2213967518u32 , 2974337319u32 , 1460139621u32 , 3022377460u32 , 1270941540u32 , 544309206u32 , 741572628u32 , 3081310440u32 , 1048777406u32 , 771578779u32 , 534663353u32 , 590134515u32 , 1545869555u32 , 1788099404u32 , 1004995609u32 , 2696521111u32 , 2185800901u32 , 4227008938u32 , 3745411061u32 , 127349794u32 , 357019207u32 , 15493316u32 , 620177523u32 , 3550926143u32 , 4287406983u32 , 533400559u32 , 3123322936u32 , 2344768153u32 , 1292033197u32 , 4145642027u32 , 2459092703u32 , 2458868732u32 , 1039356818u32 , 520649620u32 , 1333435944u32 , 636181447u32 , 155801033u32 , 368096184u32 , 868058960u32 , 1996441005u32 , 624093324u32 , 290158395u32 , 1184515296u32 , 977519882u32 , 1963517842u32 , 551624971u32 , 3368766693u32 , 3708044859u32 , 775230342u32 , 3435481251u32 , 1674115585u32 , 1776997671u32 , 3615584812u32 , 857129565u32 , 3364636024u32 , 3857554377u32 , 1265818470u32 , 57662688u32 , 4224609035u32 , 1782822870u32 , 1523977509u32 , 717168679u32 , 3984346281u32 , 4049759821u32 , 2107411667u32 , 610893786u32 , 3044528111u32 , 1543956842u32 , 3543883658u32 , 1494292933u32 , 2068578658u32 , 284653812u32 , 3377140045u32 , 1119904607u32 , 2603585436u32 , 1882551413u32 , 3336636850u32 , 1589547421u32 , 3611920660u32 , 3364582022u32 , 742853127u32 , 2591735095u32 , 2091062413u32 , 2410855252u32 , 4151148662u32 , 3553740950u32 , 1048711711u32 , 1199486522u32 , 1700718142u32 , 3707244343u32 , 951933631u32 , 3682806967u32 , 3880329584u32 , 3590914330u32 , 2817038524u32 , 4133853804u32 , 3184408810u32 , 2922547937u32 , 816344801u32 , 326494834u32 , 1507707020u32 , 2271863710u32 , 273258113u32 , 2801309061u32 , 1085252765u32 , 585557574u32 , 1312324510u32 , 1409620084u32 , 986461641u32 , 3902548497u32 , 3426316285u32 , 485064890u32 , 4119024989u32 , 3608092222u32 , 1346406727u32 , 2457901430u32 , 1689950037u32 , 3754959190u32 , 3931194778u32 , 1816790706u32 , 4208387215u32 , 315569262u32 , 54234400u32 , 2994067376u32 , 3933235587u32 , 3315223323u32 , 1381343165u32 , 3006277213u32 , 3293521275u32 , 1621808796u32 , 3496513502u32 , 1086403303u32 , 3983412185u32 , 1634030783u32 , 1733779952u32 , 3812424145u32 , 3084283302u32 , 942939809u32 , 159552655u32 , 1711900506u32 , 2274905264u32 , 970213824u32 , 3896904842u32 , 1682171771u32 , 1867791235u32 , 1855317180u32 , 2832170387u32 , 1633961095u32 , 2798901808u32 , 4103141842u32 , 3478361443u32 , 3205621890u32 , 2749033157u32 , 2619524090u32 , 2907519379u32 , 1754753923u32 , 1313537745u32 , 259654618u32 , 2250182719u32 , 3155395509u32 , 3587911337u32 , 2143535654u32 , 2190494129u32 , 3094279332u32 , 3439885968u32 , 1642132156u32 , 2979666456u32 , 909579565u32 , 3303937590u32 , 3390498297u32 , 1175213659u32 , 846747598u32 , 1531081973u32 , 2377615277u32 , 1529410224u32 , 1493588833u32 , 551601771u32 , 2225179655u32 , 1081021937u32 , 3337178u32 , 718703276u32 , 1925546115u32 , 2789342562u32 , 3855238303u32 , 1694998203u32 , 3290302249u32 , 3173400481u32 , 1159398089u32 , 44291915u32 , 3414928998u32 , 1695163510u32 , 3251283326u32 , 3281157013u32 , 3261773436u32 , 3285832223u32 , 906623274u32 , 1282825161u32 , 962337312u32 , 479697793u32 , 1227300306u32 , 1359418558u32 , 1462083347u32 , 3841841410u32 , 1026931307u32 , 3430839532u32 , 2813597207u32 , 308719458u32 , 2324745345u32 , 1954813431u32 , 883707780u32 , 904157188u32 , 2659411739u32 , 1411183032u32 , 2825708013u32 , 745064063u32 , 3897195105u32 , 3528056260u32 , 2042178864u32 , 2519983718u32 , 4003256955u32 , 1351348774u32 , 2271299428u32 , 4064148976u32 , 1582153498u32 , 1154571981u32 , 3257443589u32 , 2351838155u32 , 3668239605u32 , 2384049441u32 , 3031015162u32 , 1784113404u32 , 4110733451u32 , 149474300u32 , 1846373592u32 , 3865554921u32 , 1327970202u32 , 3482160649u32 , 2816759430u32 , 3958599450u32 , 1630221347u32 , 872051764u32 , 3717621328u32 , 3457414096u32 , 3794671005u32 , 2907088801u32 , 3101068841u32 , 3356497412u32 , 3316294029u32 , 1932517039u32 , 2047753258u32 , 970872435u32 , 83113090u32 , 340120193u32 , 1031843807u32 , 1648013096u32 , 1213081654u32 , 507264248u32 , 4123782853u32 , 2965140528u32 , 3455867731u32 , 2418868736u32 , 167018041u32 , 33840900u32 , 909665113u32 , 1697385883u32 , 3213189697u32 , 4015803665u32 , 498024801u32 , 2620153124u32 , 207807063u32 , 1289880550u32 , 748030312u32 , 3792741489u32 , 361055874u32 , 2086755244u32 , 2731266052u32 , 3949169397u32 , 1622799733u32 , 631093780u32 , 2998850704u32 , 3275662599u32 , 540984354u32 , 1306006527u32 , 1208964997u32 , 447376357u32 , 2806594463u32 , 2778411632u32 , 274055517u32 , 1647114263u32 , 487396000u32 , 3182531199u32 , 1147032844u32 , 1585700207u32 , 2491696158u32 , 297767543u32 , 1906307066u32 , 2783092694u32 , 712744979u32 , 2338483475u32 , 1328456326u32 , 496466373u32 , 3174660173u32 , 2330201181u32 , 511592759u32 , 1209573904u32 , 3434111473u32 , 625288246u32 , 513392110u32 , 3064390904u32 , 1437651958u32 , 1715378710u32 , 1343823872u32 , 1853897548u32 , 24676607u32 , 3847257422u32 , 697370994u32 , 4018656289u32 , 2196119817u32 , 613227492u32 , 3545802469u32 , 1122986055u32 , 2196595662u32 , 3564373280u32 , 1638551108u32 , 3674106722u32 , 2162760400u32 , 2070420020u32 , 2246839197u32 , 3224307212u32 , 1563912927u32 , 946506250u32 , 2146706716u32 , 2986159298u32 , 842732693u32 , 1459652027u32 , 2282986093u32 , 484169377u32 , 635163395u32 , 4131332030u32 , 3520680811u32 , 11851321u32 , 6175338u32 , 491646294u32 , 3559237039u32 , 1670804630u32 , 2112894915u32 , 1170121292u32 , 3855161187u32 , 1397943031u32 , 898289440u32 , 1048380744u32 , 503445220u32 , 4067843623u32 , 849788126u32 , 540156035u32 , 910599529u32 , 1733808494u32 , 1232676803u32 , 314533796u32 , 2352150961u32 , 3468989663u32 , 3274242198u32 , 785460432u32 , 3838527384u32 , 2785209304u32 , 471362903u32 , 1270424231u32 , 2052860768u32 , 4125580644u32 , 3978549325u32 , 1872549968u32 , 849353939u32 , 917510687u32 , 1443202521u32 , 1530204897u32 , 1048020461u32 , 1259852793u32 , 1466152561u32 , 1271998022u32 , 822105573u32 , 1310663047u32 , 1597870884u32 , 1372447217u32 , 3471797576u32 , 1886467117u32 , 3296146244u32 , 1527443701u32 , 3937177209u32 , 2790534997u32 , 2389793136u32 , 2440702496u32 , 2231254690u32 , 1289272887u32 , 1966251997u32 , 3522778558u32 , 2606069879u32 , 3342514579u32 , 3888122413u32 , 2062376914u32 , 3332067991u32 , 4197990453u32 , 1123183688u32 , 258670217u32 , 1030377645u32 , 2751698208u32 , 3021310310u32 , 3473820876u32 , 2869380463u32 , 738404731u32 , 790236809u32 , 1813694309u32 , 1265017544u32 , 2407704912u32 , 214212608u32 , 115637463u32 , 2840815696u32 , 4105776530u32 , 2897211203u32 , 428550904u32 , 1288088242u32 , 3664934712u32 , 1268358839u32 , 1857928766u32 , 3646787135u32 , 671572742u32 , 657335448u32 , 835417400u32 , 1390052554u32 , 1744675009u32 , 3200099719u32 , 1031924441u32 , 1137297938u32 , 1196494198u32 , 3192182081u32 , 4263383224u32 , 3906128438u32 , 3247137570u32 , 2108171772u32 , 53892155u32 , 2176790048u32 , 968834697u32 , 79799947u32 , 1100105663u32 , 1013528205u32 , 1904259492u32 , 1713409972u32 , 316404133u32 , 1980444132u32 , 510569370u32 , 3173477931u32 , 829316774u32 , 2248823500u32 , 322837783u32 , 1313257404u32 , 1064147050u32 , 3377397637u32 , 1961077096u32 , 3835553204u32 , 3965997140u32 , 460370253u32 , 2145215358u32 , 3423054706u32 , 290068694u32 , 2454674208u32 , 3221036252u32 , 2040992428u32 , 225950890u32 , 1805514067u32 , 482099679u32 , 2720850668u32 , 2961964106u32 , 1040004347u32 , 4016453314u32 , 2117758015u32 , 3810706127u32 , 1490652026u32 , 859546789u32 , 839481326u32 , 59137076u32 , 1960251696u32 , 3467076559u32 , 4121872099u32 , 3805722218u32 , 1248144311u32 , 2613470253u32 , 2530726984u32 , 611308476u32 , 3895686923u32 , 1751032114u32 , 1746599759u32 , 2577707114u32 , 1764594812u32 , 904682406u32 , 3119168777u32 , 3595420161u32 , 3436454788u32 , 422796352u32 , 3278498399u32 , 768955282u32 , 2776856061u32 , 3844535459u32 , 1563370450u32 , 2770211272u32 , 328232673u32 , 3167429639u32 , 2822081788u32 , 787991425u32 , 176981824u32 , 3148690945u32 , 3924127257u32 , 3172500845u32 , 2870941502u32 , 1704381425u32 , 3756296120u32 , 3359083184u32 , 3219296642u32 , 2695151648u32 , 11175636u32 , 4114207458u32 , 3017840974u32 , 1843828572u32 , 1943821868u32 , 577943792u32 , 869956525u32 , 1421968030u32 , 359587721u32 , 2509936390u32 , 776053845u32 , 601046546u32 , 1946543310u32 , 2079547615u32 , 1495418526u32 , 1488329004u32 , 582074557u32 , 3441525639u32 , 2055579803u32 , 1704100245u32 , 26369323u32 , 1853245385u32 , 2038498193u32 , 2095934344u32 , 2607187618u32 , 2832092832u32 , 1449911196u32 , 1622957280u32 , 193163341u32 , 374628926u32 , 209075522u32 , 2253042667u32 , 3125694201u32 , 3469436088u32 , 2753608321u32 , 179509946u32 , 54579969u32 , 4032437431u32 , 1899012687u32 , 1835393626u32 , 1406178112u32 , 1882820659u32 , 4039765020u32 , 2531535454u32 , 835434193u32 , 517970045u32 , 3383742429u32 , 1687463409u32 , 3677307242u32 , 2481108222u32 , 696243149u32 , 1727774507u32 , 4001891308u32 , 3350461016u32 , 603056529u32 , 1356155463u32 , 372536167u32 , 251844155u32 , 795111050u32 , 18699357u32] } ;
& SET } fn empty_string_index () -> u32 { 388u32 } } pub const ATOM_LOCALNAME__64_75_72 : LocalName = LocalName :: pack_static (0u32) ;
pub const ATOM_LOCALNAME__75_6E_69_74_73_2D_70_65_72_2D_65_6D : LocalName = LocalName :: pack_static (1u32) ;
pub const ATOM_LOCALNAME__73_65_6C_65_63_74_65_64 : LocalName = LocalName :: pack_static (2u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_54_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (3u32) ;
pub const ATOM_LOCALNAME__74_68 : LocalName = LocalName :: pack_static (4u32) ;
pub const ATOM_LOCALNAME__61_6E_64 : LocalName = LocalName :: pack_static (5u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_73_65_63_72_65_74 : LocalName = LocalName :: pack_static (6u32) ;
pub const ATOM_LOCALNAME__6D_67_6C_79_70_68 : LocalName = LocalName :: pack_static (7u32) ;
pub const ATOM_LOCALNAME__67_72_61_64 : LocalName = LocalName :: pack_static (8u32) ;
pub const ATOM_LOCALNAME__73_65_6C_65_63_74 : LocalName = LocalName :: pack_static (9u32) ;
pub const ATOM_LOCALNAME__6C_6F_77_73_72_63 : LocalName = LocalName :: pack_static (10u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_2D_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (11u32) ;
pub const ATOM_LOCALNAME__67_65_71 : LocalName = LocalName :: pack_static (12u32) ;
pub const ATOM_LOCALNAME__70_69_6E_67 : LocalName = LocalName :: pack_static (13u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_53_74_79_6C_65_54_79_70_65 : LocalName = LocalName :: pack_static (14u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_63_68_65_63_6B_65_64 : LocalName = LocalName :: pack_static (15u32) ;
pub const ATOM_LOCALNAME__6C_6E : LocalName = LocalName :: pack_static (16u32) ;
pub const ATOM_LOCALNAME__64_61_74_61_66_6F_72_6D_61_74_61_73 : LocalName = LocalName :: pack_static (17u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67 : LocalName = LocalName :: pack_static (18u32) ;
pub const ATOM_LOCALNAME__64_65_63_6C_61_72_65 : LocalName = LocalName :: pack_static (19u32) ;
pub const ATOM_LOCALNAME__6F_6E_65_72_72_6F_72 : LocalName = LocalName :: pack_static (20u32) ;
pub const ATOM_LOCALNAME__70 : LocalName = LocalName :: pack_static (21u32) ;
pub const ATOM_LOCALNAME__6D_73_75_70 : LocalName = LocalName :: pack_static (22u32) ;
pub const ATOM_LOCALNAME__6B_62_64 : LocalName = LocalName :: pack_static (23u32) ;
pub const ATOM_LOCALNAME__6D_61_72_67_69_6E_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (24u32) ;
pub const ATOM_LOCALNAME__73_76_67 : LocalName = LocalName :: pack_static (25u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72_52_65_73 : LocalName = LocalName :: pack_static (26u32) ;
pub const ATOM_LOCALNAME__77_72_61_70 : LocalName = LocalName :: pack_static (27u32) ;
pub const ATOM_LOCALNAME__68_31 : LocalName = LocalName :: pack_static (28u32) ;
pub const ATOM_LOCALNAME__6E_6F_65_6D_62_65_64 : LocalName = LocalName :: pack_static (29u32) ;
pub const ATOM_LOCALNAME__64_69_73_70_6C_61_79_73_74_79_6C_65 : LocalName = LocalName :: pack_static (30u32) ;
pub const ATOM_LOCALNAME__6E_75_6D_6F_63_74_61_76_65_73 : LocalName = LocalName :: pack_static (31u32) ;
pub const ATOM_LOCALNAME__61_64_64_72_65_73_73 : LocalName = LocalName :: pack_static (32u32) ;
pub const ATOM_LOCALNAME__61_73_79_6E_63 : LocalName = LocalName :: pack_static (33u32) ;
pub const ATOM_LOCALNAME__79_63_68_61_6E_6E_65_6C_73_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (34u32) ;
pub const ATOM_LOCALNAME__68_6F_72_69_7A_2D_6F_72_69_67_69_6E_2D_78 : LocalName = LocalName :: pack_static (35u32) ;
pub const ATOM_LOCALNAME__61_73_69_64_65 : LocalName = LocalName :: pack_static (36u32) ;
pub const ATOM_LOCALNAME__66_65_63_6F_6D_70_6F_73_69_74_65 : LocalName = LocalName :: pack_static (37u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_6C_75_72 : LocalName = LocalName :: pack_static (38u32) ;
pub const ATOM_LOCALNAME__64_69_66_66_75_73_65_63_6F_6E_73_74_61_6E_74 : LocalName = LocalName :: pack_static (39u32) ;
pub const ATOM_LOCALNAME__6C_65_71 : LocalName = LocalName :: pack_static (40u32) ;
pub const ATOM_LOCALNAME__6C_69_73_74 : LocalName = LocalName :: pack_static (41u32) ;
pub const ATOM_LOCALNAME__61_78_69_73 : LocalName = LocalName :: pack_static (42u32) ;
pub const ATOM_LOCALNAME__63_6F_64_6F_6D_61_69_6E : LocalName = LocalName :: pack_static (43u32) ;
pub const ATOM_LOCALNAME__70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (44u32) ;
pub const ATOM_LOCALNAME__76_65_72_79_74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (45u32) ;
pub const ATOM_LOCALNAME__7A_6F_6F_6D_61_6E_64_70_61_6E : LocalName = LocalName :: pack_static (46u32) ;
pub const ATOM_LOCALNAME__66_65_73_70_6F_74_6C_69_67_68_74 : LocalName = LocalName :: pack_static (47u32) ;
pub const ATOM_LOCALNAME__76_61_6C_75_65_73 : LocalName = LocalName :: pack_static (48u32) ;
pub const ATOM_LOCALNAME__72_73_70_61_63_65 : LocalName = LocalName :: pack_static (49u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_6D_69_64 : LocalName = LocalName :: pack_static (50u32) ;
pub const ATOM_LOCALNAME__64_69_72_6E_61_6D_65 : LocalName = LocalName :: pack_static (51u32) ;
pub const ATOM_LOCALNAME__73_63_72_6F_6C_6C_64_65_6C_61_79 : LocalName = LocalName :: pack_static (52u32) ;
pub const ATOM_LOCALNAME__6F_6E_6C_6F_61_64 : LocalName = LocalName :: pack_static (53u32) ;
pub const ATOM_LOCALNAME__64_61_74_61_73_72_63 : LocalName = LocalName :: pack_static (54u32) ;
pub const ATOM_LOCALNAME__72_65_70_6C_61_63_65 : LocalName = LocalName :: pack_static (55u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_6D_65_74_68_6F_64 : LocalName = LocalName :: pack_static (56u32) ;
pub const ATOM_LOCALNAME__70_72_65_73_65_72_76_65_41_6C_70_68_61 : LocalName = LocalName :: pack_static (57u32) ;
pub const ATOM_LOCALNAME__66_6C_6F_6F_64_2D_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (58u32) ;
pub const ATOM_LOCALNAME__6D_69_6E_73_69_7A_65 : LocalName = LocalName :: pack_static (59u32) ;
pub const ATOM_LOCALNAME__66_65_6D_65_72_67_65_6E_6F_64_65 : LocalName = LocalName :: pack_static (60u32) ;
pub const ATOM_LOCALNAME__63_79 : LocalName = LocalName :: pack_static (61u32) ;
pub const ATOM_LOCALNAME__66_65_53_70_65_63_75_6C_61_72_4C_69_67_68_74_69_6E_67 : LocalName = LocalName :: pack_static (62u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_6E_6F_76_61_6C_69_64_61_74_65 : LocalName = LocalName :: pack_static (63u32) ;
pub const ATOM_LOCALNAME__73_74_6F_70_2D_6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (64u32) ;
pub const ATOM_LOCALNAME__7A : LocalName = LocalName :: pack_static (65u32) ;
pub const ATOM_LOCALNAME__6D_75_6E_64_65_72 : LocalName = LocalName :: pack_static (66u32) ;
pub const ATOM_LOCALNAME__73_6C_6F_70_65 : LocalName = LocalName :: pack_static (67u32) ;
pub const ATOM_LOCALNAME__63_73_79_6D_62_6F_6C : LocalName = LocalName :: pack_static (68u32) ;
pub const ATOM_LOCALNAME__69_6E_64_65_78 : LocalName = LocalName :: pack_static (69u32) ;
pub const ATOM_LOCALNAME__75_6E_69_6F_6E : LocalName = LocalName :: pack_static (70u32) ;
pub const ATOM_LOCALNAME__73_6C_6F_74 : LocalName = LocalName :: pack_static (71u32) ;
pub const ATOM_LOCALNAME__6D_69_73_73_69_6E_67_2D_67_6C_79_70_68 : LocalName = LocalName :: pack_static (72u32) ;
pub const ATOM_LOCALNAME__78_2D_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (73u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_62_6C_63_6C_69_63_6B : LocalName = LocalName :: pack_static (74u32) ;
pub const ATOM_LOCALNAME__6C_69_6D_69_74_69_6E_67_43_6F_6E_65_41_6E_67_6C_65 : LocalName = LocalName :: pack_static (75u32) ;
pub const ATOM_LOCALNAME__61_70_70_72_6F_78 : LocalName = LocalName :: pack_static (76u32) ;
pub const ATOM_LOCALNAME__78 : LocalName = LocalName :: pack_static (77u32) ;
pub const ATOM_LOCALNAME__69_6D_61_67_69_6E_61_72_79_69 : LocalName = LocalName :: pack_static (78u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_65_72_2D_65_76_65_6E_74_73 : LocalName = LocalName :: pack_static (79u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_77_65_69_67_68_74 : LocalName = LocalName :: pack_static (80u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_43_6F_6E_73_74_61_6E_74 : LocalName = LocalName :: pack_static (81u32) ;
pub const ATOM_LOCALNAME__69_6E_32 : LocalName = LocalName :: pack_static (82u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_63_75_73_6F_75_74 : LocalName = LocalName :: pack_static (83u32) ;
pub const ATOM_LOCALNAME__73_74_65_70 : LocalName = LocalName :: pack_static (84u32) ;
pub const ATOM_LOCALNAME__77_62_72 : LocalName = LocalName :: pack_static (85u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_2D_6F_72_69_65_6E_74_61_74_69_6F_6E_2D_76_65_72_74_69_63_61_6C : LocalName = LocalName :: pack_static (86u32) ;
pub const ATOM_LOCALNAME__66_65_42_6C_65_6E_64 : LocalName = LocalName :: pack_static (87u32) ;
pub const ATOM_LOCALNAME__73_63_72_69_70_74_73_69_7A_65_6D_75_6C_74_69_70_6C_69_65_72 : LocalName = LocalName :: pack_static (88u32) ;
pub const ATOM_LOCALNAME__68_73_70_61_63_65 : LocalName = LocalName :: pack_static (89u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_73_70_61_6E : LocalName = LocalName :: pack_static (90u32) ;
pub const ATOM_LOCALNAME__70_72_6F_67_72_65_73_73 : LocalName = LocalName :: pack_static (91u32) ;
pub const ATOM_LOCALNAME__61_75_74_6F_63_6F_6D_70_6C_65_74_65 : LocalName = LocalName :: pack_static (92u32) ;
pub const ATOM_LOCALNAME__72_75_6C_65 : LocalName = LocalName :: pack_static (93u32) ;
pub const ATOM_LOCALNAME__63_65_69_6C_69_6E_67 : LocalName = LocalName :: pack_static (94u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6F_77_6E_73 : LocalName = LocalName :: pack_static (95u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_72_63_65_70_74 : LocalName = LocalName :: pack_static (96u32) ;
pub const ATOM_LOCALNAME__73_70_61_63_65 : LocalName = LocalName :: pack_static (97u32) ;
pub const ATOM_LOCALNAME__6E_65_78_74_69_64 : LocalName = LocalName :: pack_static (98u32) ;
pub const ATOM_LOCALNAME__66_65_4D_6F_72_70_68_6F_6C_6F_67_79 : LocalName = LocalName :: pack_static (99u32) ;
pub const ATOM_LOCALNAME__63_65_6C_6C_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (100u32) ;
pub const ATOM_LOCALNAME__73_74_61_72_74 : LocalName = LocalName :: pack_static (101u32) ;
pub const ATOM_LOCALNAME__63_61_6C_63_4D_6F_64_65 : LocalName = LocalName :: pack_static (102u32) ;
pub const ATOM_LOCALNAME__72_65_6D : LocalName = LocalName :: pack_static (103u32) ;
pub const ATOM_LOCALNAME__6C_65_67_65_6E_64 : LocalName = LocalName :: pack_static (104u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_74_61_72_74 : LocalName = LocalName :: pack_static (105u32) ;
pub const ATOM_LOCALNAME__70_61_74_68 : LocalName = LocalName :: pack_static (106u32) ;
pub const ATOM_LOCALNAME__66_65_46_75_6E_63_47 : LocalName = LocalName :: pack_static (107u32) ;
pub const ATOM_LOCALNAME__6F_6E_61_66_74_65_72_70_72_69_6E_74 : LocalName = LocalName :: pack_static (108u32) ;
pub const ATOM_LOCALNAME__70_72_69_6D_69_74_69_76_65_55_6E_69_74_73 : LocalName = LocalName :: pack_static (109u32) ;
pub const ATOM_LOCALNAME__61_6C_74_69_6D_67 : LocalName = LocalName :: pack_static (110u32) ;
pub const ATOM_LOCALNAME__72_65_66_58 : LocalName = LocalName :: pack_static (111u32) ;
pub const ATOM_LOCALNAME__76_69_65_77_42_6F_78 : LocalName = LocalName :: pack_static (112u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_6C_69_6E_65_73 : LocalName = LocalName :: pack_static (113u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_43_6F_6C_6F_72 : LocalName = LocalName :: pack_static (114u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_55_6E_69_74_73 : LocalName = LocalName :: pack_static (115u32) ;
pub const ATOM_LOCALNAME__6F_70_74_69_6D_75_6D : LocalName = LocalName :: pack_static (116u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_6F_75_6E_63_65 : LocalName = LocalName :: pack_static (117u32) ;
pub const ATOM_LOCALNAME__6C_65_6E_67_74_68_41_64_6A_75_73_74 : LocalName = LocalName :: pack_static (118u32) ;
pub const ATOM_LOCALNAME__73_72_63_6C_61_6E_67 : LocalName = LocalName :: pack_static (119u32) ;
pub const ATOM_LOCALNAME__61_6C_74_74_65_78_74 : LocalName = LocalName :: pack_static (120u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_62_61_63_6B_67_72_6F_75_6E_64 : LocalName = LocalName :: pack_static (121u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_72_65_71_75_69_72_65_64 : LocalName = LocalName :: pack_static (122u32) ;
pub const ATOM_LOCALNAME__65_6C_6C_69_70_73_65 : LocalName = LocalName :: pack_static (123u32) ;
pub const ATOM_LOCALNAME__69_66_72_61_6D_65 : LocalName = LocalName :: pack_static (124u32) ;
pub const ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68_49_74_65_6D : LocalName = LocalName :: pack_static (125u32) ;
pub const ATOM_LOCALNAME__76_2D_68_61_6E_67_69_6E_67 : LocalName = LocalName :: pack_static (126u32) ;
pub const ATOM_LOCALNAME__6C_61_6E_67 : LocalName = LocalName :: pack_static (127u32) ;
pub const ATOM_LOCALNAME__66_65_63_6F_6C_6F_72_6D_61_74_72_69_78 : LocalName = LocalName :: pack_static (128u32) ;
pub const ATOM_LOCALNAME__61_6C_69_67_6E_6D_65_6E_74_2D_62_61_73_65_6C_69_6E_65 : LocalName = LocalName :: pack_static (129u32) ;
pub const ATOM_LOCALNAME__6D_69 : LocalName = LocalName :: pack_static (130u32) ;
pub const ATOM_LOCALNAME__74_74 : LocalName = LocalName :: pack_static (131u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B : LocalName = LocalName :: pack_static (132u32) ;
pub const ATOM_LOCALNAME__6D_61_63_72_6F_73 : LocalName = LocalName :: pack_static (133u32) ;
pub const ATOM_LOCALNAME__66_6F_72 : LocalName = LocalName :: pack_static (134u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_77_68_65_65_6C : LocalName = LocalName :: pack_static (135u32) ;
pub const ATOM_LOCALNAME__73_63_72_69_70_74 : LocalName = LocalName :: pack_static (136u32) ;
pub const ATOM_LOCALNAME__74_62_72_65_61_6B : LocalName = LocalName :: pack_static (137u32) ;
pub const ATOM_LOCALNAME__6F_76_65_72_66_6C_6F_77 : LocalName = LocalName :: pack_static (138u32) ;
pub const ATOM_LOCALNAME__75_6E_64_65_72_6C_69_6E_65_2D_70_6F_73_69_74_69_6F_6E : LocalName = LocalName :: pack_static (139u32) ;
pub const ATOM_LOCALNAME__73_74_64_64_65_76_69_61_74_69_6F_6E : LocalName = LocalName :: pack_static (140u32) ;
pub const ATOM_LOCALNAME__70_72_65_66_65_74_63_68 : LocalName = LocalName :: pack_static (141u32) ;
pub const ATOM_LOCALNAME__64_6C : LocalName = LocalName :: pack_static (142u32) ;
pub const ATOM_LOCALNAME__63_68_61_72 : LocalName = LocalName :: pack_static (143u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_2D_61_6E_63_68_6F_72 : LocalName = LocalName :: pack_static (144u32) ;
pub const ATOM_LOCALNAME__6E_61_76 : LocalName = LocalName :: pack_static (145u32) ;
pub const ATOM_LOCALNAME__73_63_68_65_6D_65 : LocalName = LocalName :: pack_static (146u32) ;
pub const ATOM_LOCALNAME__62_67_73_6F_75_6E_64 : LocalName = LocalName :: pack_static (147u32) ;
pub const ATOM_LOCALNAME__75_6E_69_63_6F_64_65_2D_72_61_6E_67_65 : LocalName = LocalName :: pack_static (148u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_48_65_69_67_68_74 : LocalName = LocalName :: pack_static (149u32) ;
pub const ATOM_LOCALNAME__62_72 : LocalName = LocalName :: pack_static (150u32) ;
pub const ATOM_LOCALNAME__6E_6F_6E_65 : LocalName = LocalName :: pack_static (151u32) ;
pub const ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_74_79_70_65 : LocalName = LocalName :: pack_static (152u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_63_6F_6E_74_65_6E_74_75_6E_69_74_73 : LocalName = LocalName :: pack_static (153u32) ;
pub const ATOM_LOCALNAME__74_66_6F_6F_74 : LocalName = LocalName :: pack_static (154u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_6F_77_65_6E_74_65_72 : LocalName = LocalName :: pack_static (155u32) ;
pub const ATOM_LOCALNAME__63_72_6F_73_73_6F_72_69_67_69_6E : LocalName = LocalName :: pack_static (156u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_4C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (157u32) ;
pub const ATOM_LOCALNAME__66_65_64_69_73_74_61_6E_74_6C_69_67_68_74 : LocalName = LocalName :: pack_static (158u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (159u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_75_74 : LocalName = LocalName :: pack_static (160u32) ;
pub const ATOM_LOCALNAME__68_65_61_64_65_72_73 : LocalName = LocalName :: pack_static (161u32) ;
pub const ATOM_LOCALNAME__75_73_65 : LocalName = LocalName :: pack_static (162u32) ;
pub const ATOM_LOCALNAME__69_6E_74 : LocalName = LocalName :: pack_static (163u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_73_74_79_6C_65 : LocalName = LocalName :: pack_static (164u32) ;
pub const ATOM_LOCALNAME__72_6F_77_73_70_61_6E : LocalName = LocalName :: pack_static (165u32) ;
pub const ATOM_LOCALNAME__73_77_69_74_63_68 : LocalName = LocalName :: pack_static (166u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (167u32) ;
pub const ATOM_LOCALNAME__64_6F_6D_61_69_6E : LocalName = LocalName :: pack_static (168u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_68_72_65_66 : LocalName = LocalName :: pack_static (169u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_73_74_61_72_74 : LocalName = LocalName :: pack_static (170u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_61_63_74_75_61_74_65 : LocalName = LocalName :: pack_static (171u32) ;
pub const ATOM_LOCALNAME__6D_73 : LocalName = LocalName :: pack_static (172u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_6F_70_79 : LocalName = LocalName :: pack_static (173u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_63_72_6F_6C_6C : LocalName = LocalName :: pack_static (174u32) ;
pub const ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68_69_74_65_6D : LocalName = LocalName :: pack_static (175u32) ;
pub const ATOM_LOCALNAME__70_72_69_6D_69_74_69_76_65_75_6E_69_74_73 : LocalName = LocalName :: pack_static (176u32) ;
pub const ATOM_LOCALNAME__62_76_61_72 : LocalName = LocalName :: pack_static (177u32) ;
pub const ATOM_LOCALNAME__6D_70_72_65_73_63_72_69_70_74_73 : LocalName = LocalName :: pack_static (178u32) ;
pub const ATOM_LOCALNAME__63_65_6E_74_65_72 : LocalName = LocalName :: pack_static (179u32) ;
pub const ATOM_LOCALNAME__6D_69_6E : LocalName = LocalName :: pack_static (180u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_77_65_69_67_68_74 : LocalName = LocalName :: pack_static (181u32) ;
pub const ATOM_LOCALNAME__78_6D_70 : LocalName = LocalName :: pack_static (182u32) ;
pub const ATOM_LOCALNAME__65_71_75_61_6C_63_6F_6C_75_6D_6E_73 : LocalName = LocalName :: pack_static (183u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_6B : LocalName = LocalName :: pack_static (184u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6D_61_78 : LocalName = LocalName :: pack_static (185u32) ;
pub const ATOM_LOCALNAME__6D_69_6E_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (186u32) ;
pub const ATOM_LOCALNAME__73_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (187u32) ;
pub const ATOM_LOCALNAME__6E_6F_72_65_73_69_7A_65 : LocalName = LocalName :: pack_static (188u32) ;
pub const ATOM_LOCALNAME__69_6E_76_65_72_73_65 : LocalName = LocalName :: pack_static (189u32) ;
pub const ATOM_LOCALNAME__6D_61_78_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (190u32) ;
pub const ATOM_LOCALNAME__70_61_72_74_69_61_6C_64_69_66_66 : LocalName = LocalName :: pack_static (191u32) ;
pub const ATOM_LOCALNAME__72_65_61_6C_73 : LocalName = LocalName :: pack_static (192u32) ;
pub const ATOM_LOCALNAME__66_65_6D_65_72_67_65 : LocalName = LocalName :: pack_static (193u32) ;
pub const ATOM_LOCALNAME__64_6F_6D_61_69_6E_6F_66_61_70_70_6C_69_63_61_74_69_6F_6E : LocalName = LocalName :: pack_static (194u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_3A_62_61_73_65 : LocalName = LocalName :: pack_static (195u32) ;
pub const ATOM_LOCALNAME__6D_66_72_61_63 : LocalName = LocalName :: pack_static (196u32) ;
pub const ATOM_LOCALNAME__67_32 : LocalName = LocalName :: pack_static (197u32) ;
pub const ATOM_LOCALNAME__6F_6E_69_6E_70_75_74 : LocalName = LocalName :: pack_static (198u32) ;
pub const ATOM_LOCALNAME__6D_61_74_72_69_78 : LocalName = LocalName :: pack_static (199u32) ;
pub const ATOM_LOCALNAME__73_75_72_66_61_63_65_73_63_61_6C_65 : LocalName = LocalName :: pack_static (200u32) ;
pub const ATOM_LOCALNAME__70_69_65_63_65 : LocalName = LocalName :: pack_static (201u32) ;
pub const ATOM_LOCALNAME__70_6F_77_65_72 : LocalName = LocalName :: pack_static (202u32) ;
pub const ATOM_LOCALNAME__69_6E_70_75_74 : LocalName = LocalName :: pack_static (203u32) ;
pub const ATOM_LOCALNAME__71 : LocalName = LocalName :: pack_static (204u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_2D_74_65_6D_70_6C_61_74_65 : LocalName = LocalName :: pack_static (205u32) ;
pub const ATOM_LOCALNAME__74_61_62_6C_65 : LocalName = LocalName :: pack_static (206u32) ;
pub const ATOM_LOCALNAME__66_65_63_6F_6E_76_6F_6C_76_65_6D_61_74_72_69_78 : LocalName = LocalName :: pack_static (207u32) ;
pub const ATOM_LOCALNAME__61_70_70_6C_79 : LocalName = LocalName :: pack_static (208u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_70_61_74_68 : LocalName = LocalName :: pack_static (209u32) ;
pub const ATOM_LOCALNAME__68_74_74_70_2D_65_71_75_69_76 : LocalName = LocalName :: pack_static (210u32) ;
pub const ATOM_LOCALNAME__64_69_73_63_61_72_64 : LocalName = LocalName :: pack_static (211u32) ;
pub const ATOM_LOCALNAME__63_6F_6D_70_6C_65_78_65_73 : LocalName = LocalName :: pack_static (212u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72 : LocalName = LocalName :: pack_static (213u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_57_69_64_74_68 : LocalName = LocalName :: pack_static (214u32) ;
pub const ATOM_LOCALNAME__6C_69_73_74_69_6E_67 : LocalName = LocalName :: pack_static (215u32) ;
pub const ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_55_6E_69_74_73 : LocalName = LocalName :: pack_static (216u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6D_69_6E : LocalName = LocalName :: pack_static (217u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_63_6F_75_6E_74 : LocalName = LocalName :: pack_static (218u32) ;
pub const ATOM_LOCALNAME__74_72_61_63_6B : LocalName = LocalName :: pack_static (219u32) ;
pub const ATOM_LOCALNAME__67_72_6F_75_70_61_6C_69_67_6E : LocalName = LocalName :: pack_static (220u32) ;
pub const ATOM_LOCALNAME__66_65_44_69_73_74_61_6E_74_4C_69_67_68_74 : LocalName = LocalName :: pack_static (221u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B_43_6F_6E_74_65_6E_74_55_6E_69_74_73 : LocalName = LocalName :: pack_static (222u32) ;
pub const ATOM_LOCALNAME__61_72_63_74_61_6E_68 : LocalName = LocalName :: pack_static (223u32) ;
pub const ATOM_LOCALNAME__63_73_63_68 : LocalName = LocalName :: pack_static (224u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_61_63_74_69_76_61_74_65 : LocalName = LocalName :: pack_static (225u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_65_61_64_79_73_74_61_74_65_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (226u32) ;
pub const ATOM_LOCALNAME__6F_6E_6F_6E_6C_69_6E_65 : LocalName = LocalName :: pack_static (227u32) ;
pub const ATOM_LOCALNAME__72_6F_77_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (228u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_63_75_74 : LocalName = LocalName :: pack_static (229u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6C_61_62_65_6C_6C_65_64_62_79 : LocalName = LocalName :: pack_static (230u32) ;
pub const ATOM_LOCALNAME__73_75_70_65_72_73_63_72_69_70_74_73_68_69_66_74 : LocalName = LocalName :: pack_static (231u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_61_74_61_73_65_74_63_6F_6D_70_6C_65_74_65 : LocalName = LocalName :: pack_static (232u32) ;
pub const ATOM_LOCALNAME__65_6D_62_65_64 : LocalName = LocalName :: pack_static (233u32) ;
pub const ATOM_LOCALNAME__65_6C_65_76_61_74_69_6F_6E : LocalName = LocalName :: pack_static (234u32) ;
pub const ATOM_LOCALNAME__61_72_63_74_61_6E : LocalName = LocalName :: pack_static (235u32) ;
pub const ATOM_LOCALNAME__6C_65_74_74_65_72_2D_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (236u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_73_6F_72_74 : LocalName = LocalName :: pack_static (237u32) ;
pub const ATOM_LOCALNAME__6D_74_64 : LocalName = LocalName :: pack_static (238u32) ;
pub const ATOM_LOCALNAME__6B_65_79_73_70_6C_69_6E_65_73 : LocalName = LocalName :: pack_static (239u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_69_6E_69_73_68 : LocalName = LocalName :: pack_static (240u32) ;
pub const ATOM_LOCALNAME__66_65_43_6F_6D_70_6F_6E_65_6E_74_54_72_61_6E_73_66_65_72 : LocalName = LocalName :: pack_static (241u32) ;
pub const ATOM_LOCALNAME__61_75_74_6F_70_6C_61_79 : LocalName = LocalName :: pack_static (242u32) ;
pub const ATOM_LOCALNAME__70_61_6E_6F_73_65_2D_31 : LocalName = LocalName :: pack_static (243u32) ;
pub const ATOM_LOCALNAME__68_35 : LocalName = LocalName :: pack_static (244u32) ;
pub const ATOM_LOCALNAME__73_63_6F_70_65 : LocalName = LocalName :: pack_static (245u32) ;
pub const ATOM_LOCALNAME__64_65_74_65_72_6D_69_6E_61_6E_74 : LocalName = LocalName :: pack_static (246u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_65_6C_55_6E_69_74_4C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (247u32) ;
pub const ATOM_LOCALNAME__78_31 : LocalName = LocalName :: pack_static (248u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_69_6E_76_61_6C_69_64 : LocalName = LocalName :: pack_static (249u32) ;
pub const ATOM_LOCALNAME__68_61_6E_64_6C_65_72 : LocalName = LocalName :: pack_static (250u32) ;
pub const ATOM_LOCALNAME__6E_65_71 : LocalName = LocalName :: pack_static (251u32) ;
pub const ATOM_LOCALNAME__61_72_63_63_73_63_68 : LocalName = LocalName :: pack_static (252u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B : LocalName = LocalName :: pack_static (253u32) ;
pub const ATOM_LOCALNAME__6F_75_74_70_75_74 : LocalName = LocalName :: pack_static (254u32) ;
pub const ATOM_LOCALNAME__75_73_65_6D_61_70 : LocalName = LocalName :: pack_static (255u32) ;
pub const ATOM_LOCALNAME__70_72_65_73_65_72_76_65_41_73_70_65_63_74_52_61_74_69_6F : LocalName = LocalName :: pack_static (256u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_72_6F_6C_65 : LocalName = LocalName :: pack_static (257u32) ;
pub const ATOM_LOCALNAME__66_65_6F_66_66_73_65_74 : LocalName = LocalName :: pack_static (258u32) ;
pub const ATOM_LOCALNAME__78_72_65_66 : LocalName = LocalName :: pack_static (259u32) ;
pub const ATOM_LOCALNAME__6B_65_79_67_65_6E : LocalName = LocalName :: pack_static (260u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_6F_77_65_78_69_74 : LocalName = LocalName :: pack_static (261u32) ;
pub const ATOM_LOCALNAME__73_74_72_69_6E_67 : LocalName = LocalName :: pack_static (262u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_74_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (263u32) ;
pub const ATOM_LOCALNAME__76_2D_69_64_65_6F_67_72_61_70_68_69_63 : LocalName = LocalName :: pack_static (264u32) ;
pub const ATOM_LOCALNAME__6D_61_6E_69_66_65_73_74 : LocalName = LocalName :: pack_static (265u32) ;
pub const ATOM_LOCALNAME__64_65_67_72_65_65 : LocalName = LocalName :: pack_static (266u32) ;
pub const ATOM_LOCALNAME__63_69 : LocalName = LocalName :: pack_static (267u32) ;
pub const ATOM_LOCALNAME__6D_73_74_79_6C_65 : LocalName = LocalName :: pack_static (268u32) ;
pub const ATOM_LOCALNAME__61_62_62_72 : LocalName = LocalName :: pack_static (269u32) ;
pub const ATOM_LOCALNAME__66_65_66_75_6E_63_62 : LocalName = LocalName :: pack_static (270u32) ;
pub const ATOM_LOCALNAME__68_32 : LocalName = LocalName :: pack_static (271u32) ;
pub const ATOM_LOCALNAME__6C_6F_67_62_61_73_65 : LocalName = LocalName :: pack_static (272u32) ;
pub const ATOM_LOCALNAME__61_6C_69_6E_6B : LocalName = LocalName :: pack_static (273u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (274u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D : LocalName = LocalName :: pack_static (275u32) ;
pub const ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68_64_65_66 : LocalName = LocalName :: pack_static (276u32) ;
pub const ATOM_LOCALNAME__64_69_73_70_6C_61_79 : LocalName = LocalName :: pack_static (277u32) ;
pub const ATOM_LOCALNAME__6F_72_69_65_6E_74 : LocalName = LocalName :: pack_static (278u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70 : LocalName = LocalName :: pack_static (279u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_70_74 : LocalName = LocalName :: pack_static (280u32) ;
pub const ATOM_LOCALNAME__61_72_61_62_69_63_2D_66_6F_72_6D : LocalName = LocalName :: pack_static (281u32) ;
pub const ATOM_LOCALNAME__6F_6E_6C_6F_73_65_63_61_70_74_75_72_65 : LocalName = LocalName :: pack_static (282u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_70_61_74_68 : LocalName = LocalName :: pack_static (283u32) ;
pub const ATOM_LOCALNAME__6D_74_72 : LocalName = LocalName :: pack_static (284u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_3A_73_70_61_63_65 : LocalName = LocalName :: pack_static (285u32) ;
pub const ATOM_LOCALNAME__79_32 : LocalName = LocalName :: pack_static (286u32) ;
pub const ATOM_LOCALNAME__6D_65_6E_75 : LocalName = LocalName :: pack_static (287u32) ;
pub const ATOM_LOCALNAME__70_69_63_74_75_72_65 : LocalName = LocalName :: pack_static (288u32) ;
pub const ATOM_LOCALNAME__63_6F_64_65_62_61_73_65 : LocalName = LocalName :: pack_static (289u32) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74_78 : LocalName = LocalName :: pack_static (290u32) ;
pub const ATOM_LOCALNAME__72_61_74_69_6F_6E_61_6C_73 : LocalName = LocalName :: pack_static (291u32) ;
pub const ATOM_LOCALNAME__65_71 : LocalName = LocalName :: pack_static (292u32) ;
pub const ATOM_LOCALNAME__73_65_74 : LocalName = LocalName :: pack_static (293u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6D_75_6C_74_69_73_65_6C_65_63_74_61_62_6C_65 : LocalName = LocalName :: pack_static (294u32) ;
pub const ATOM_LOCALNAME__73_79_6D_6D_65_74_72_69_63 : LocalName = LocalName :: pack_static (295u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6C_69_6E_65_6A_6F_69_6E : LocalName = LocalName :: pack_static (296u32) ;
pub const ATOM_LOCALNAME__6D_69_6E_75_73 : LocalName = LocalName :: pack_static (297u32) ;
pub const ATOM_LOCALNAME__74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (298u32) ;
pub const ATOM_LOCALNAME__73_75_62_73_63_72_69_70_74_73_68_69_66_74 : LocalName = LocalName :: pack_static (299u32) ;
pub const ATOM_LOCALNAME__6C_73_70_61_63_65 : LocalName = LocalName :: pack_static (300u32) ;
pub const ATOM_LOCALNAME__70_69_65_63_65_77_69_73_65 : LocalName = LocalName :: pack_static (301u32) ;
pub const ATOM_LOCALNAME__6D_65_64_69_61_6E : LocalName = LocalName :: pack_static (302u32) ;
pub const ATOM_LOCALNAME__65_78_74_65_72_6E_61_6C_72_65_73_6F_75_72_63_65_73_72_65_71_75_69_72_65_64 : LocalName = LocalName :: pack_static (303u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_65_6E_64 : LocalName = LocalName :: pack_static (304u32) ;
pub const ATOM_LOCALNAME__72_61_64_69_75_73 : LocalName = LocalName :: pack_static (305u32) ;
pub const ATOM_LOCALNAME__72_65_66_59 : LocalName = LocalName :: pack_static (306u32) ;
pub const ATOM_LOCALNAME__73_65_61_6D_6C_65_73_73 : LocalName = LocalName :: pack_static (307u32) ;
pub const ATOM_LOCALNAME__76_65_72_73_69_6F_6E : LocalName = LocalName :: pack_static (308u32) ;
pub const ATOM_LOCALNAME__65_6D : LocalName = LocalName :: pack_static (309u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_73_74_79_6C_65_74_79_70_65 : LocalName = LocalName :: pack_static (310u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65 : LocalName = LocalName :: pack_static (311u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (312u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6D_6F_76_65 : LocalName = LocalName :: pack_static (313u32) ;
pub const ATOM_LOCALNAME__64_69_76_65_72_67_65_6E_63_65 : LocalName = LocalName :: pack_static (314u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_61_67_65_73_68_6F_77 : LocalName = LocalName :: pack_static (315u32) ;
pub const ATOM_LOCALNAME__74_61_6E : LocalName = LocalName :: pack_static (316u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_64_75_72 : LocalName = LocalName :: pack_static (317u32) ;
pub const ATOM_LOCALNAME__73_70_65_6C_6C_63_68_65_63_6B : LocalName = LocalName :: pack_static (318u32) ;
pub const ATOM_LOCALNAME__66_65_46_6C_6F_6F_64 : LocalName = LocalName :: pack_static (319u32) ;
pub const ATOM_LOCALNAME__66_65_46_75_6E_63_52 : LocalName = LocalName :: pack_static (320u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_6D_69_6C_79 : LocalName = LocalName :: pack_static (321u32) ;
pub const ATOM_LOCALNAME__6F_6E_61_66_74_65_72_75_70_64_61_74_65 : LocalName = LocalName :: pack_static (322u32) ;
pub const ATOM_LOCALNAME__63_78 : LocalName = LocalName :: pack_static (323u32) ;
pub const ATOM_LOCALNAME__73_63_72_69_70_74_6C_65_76_65_6C : LocalName = LocalName :: pack_static (324u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_73_70_61_6E : LocalName = LocalName :: pack_static (325u32) ;
pub const ATOM_LOCALNAME__69_73_69_6E_64_65_78 : LocalName = LocalName :: pack_static (326u32) ;
pub const ATOM_LOCALNAME__73_72_63 : LocalName = LocalName :: pack_static (327u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_73_65_6C_65_63_74_65_64 : LocalName = LocalName :: pack_static (328u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_65_6C_6D_61_74_72_69_78 : LocalName = LocalName :: pack_static (329u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_76_65_65_6E_64 : LocalName = LocalName :: pack_static (330u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_64_72_6F_70_65_66_66_65_63_74 : LocalName = LocalName :: pack_static (331u32) ;
pub const ATOM_LOCALNAME__66_65_67_61_75_73_73_69_61_6E_62_6C_75_72 : LocalName = LocalName :: pack_static (332u32) ;
pub const ATOM_LOCALNAME__73_74_69_74_63_68_74_69_6C_65_73 : LocalName = LocalName :: pack_static (333u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64 : LocalName = LocalName :: pack_static (334u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_64_72_6F_70 : LocalName = LocalName :: pack_static (335u32) ;
pub const ATOM_LOCALNAME__70_61_74_68_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (336u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_67_69_6E : LocalName = LocalName :: pack_static (337u32) ;
pub const ATOM_LOCALNAME__6E_6F_68_72_65_66 : LocalName = LocalName :: pack_static (338u32) ;
pub const ATOM_LOCALNAME__68_6F_72_69_7A_2D_61_64_76_2D_78 : LocalName = LocalName :: pack_static (339u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_65_6C_6C_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (340u32) ;
pub const ATOM_LOCALNAME__76_6C_69_6E_6B : LocalName = LocalName :: pack_static (341u32) ;
pub const ATOM_LOCALNAME__63_75_72_6C : LocalName = LocalName :: pack_static (342u32) ;
pub const ATOM_LOCALNAME__66_69_67_75_72_65 : LocalName = LocalName :: pack_static (343u32) ;
pub const ATOM_LOCALNAME__70_72_65_73_65_72_76_65_61_73_70_65_63_74_72_61_74_69_6F : LocalName = LocalName :: pack_static (344u32) ;
pub const ATOM_LOCALNAME__73_74_61_74_65_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (345u32) ;
pub const ATOM_LOCALNAME__63_6F_73_68 : LocalName = LocalName :: pack_static (346u32) ;
pub const ATOM_LOCALNAME__6D_6C_61_62_65_6C_65_64_74_72 : LocalName = LocalName :: pack_static (347u32) ;
pub const ATOM_LOCALNAME__73_75_62_73_65_74 : LocalName = LocalName :: pack_static (348u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72_2D_72_65_6E_64_65_72_69_6E_67 : LocalName = LocalName :: pack_static (349u32) ;
pub const ATOM_LOCALNAME__73_75_72_66_61_63_65_53_63_61_6C_65 : LocalName = LocalName :: pack_static (350u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_75_70_64_61_74_65 : LocalName = LocalName :: pack_static (351u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_61_63_74_69_6F_6E : LocalName = LocalName :: pack_static (352u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_61_72_47_72_61_64_69_65_6E_74 : LocalName = LocalName :: pack_static (353u32) ;
pub const ATOM_LOCALNAME__6B_69_6E_64 : LocalName = LocalName :: pack_static (354u32) ;
pub const ATOM_LOCALNAME__74_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (355u32) ;
pub const ATOM_LOCALNAME__72_78 : LocalName = LocalName :: pack_static (356u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_64_65_73_63_72_69_62_65_64_62_79 : LocalName = LocalName :: pack_static (357u32) ;
pub const ATOM_LOCALNAME__61_72_67 : LocalName = LocalName :: pack_static (358u32) ;
pub const ATOM_LOCALNAME__73_70_72_65_61_64_4D_65_74_68_6F_64 : LocalName = LocalName :: pack_static (359u32) ;
pub const ATOM_LOCALNAME__70_72_6F_66_69_6C_65 : LocalName = LocalName :: pack_static (360u32) ;
pub const ATOM_LOCALNAME__63_61_72_74_65_73_69_61_6E_70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (361u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B_63_6F_6E_74_65_6E_74_75_6E_69_74_73 : LocalName = LocalName :: pack_static (362u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_2D_72_75_6C_65 : LocalName = LocalName :: pack_static (363u32) ;
pub const ATOM_LOCALNAME__72_65_76_65_72_73_65_64 : LocalName = LocalName :: pack_static (364u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_76_61_72_69_61_6E_74 : LocalName = LocalName :: pack_static (365u32) ;
pub const ATOM_LOCALNAME__64_69_66_66_75_73_65_43_6F_6E_73_74_61_6E_74 : LocalName = LocalName :: pack_static (366u32) ;
pub const ATOM_LOCALNAME__63_6F_64_65 : LocalName = LocalName :: pack_static (367u32) ;
pub const ATOM_LOCALNAME__66_69_67_63_61_70_74_69_6F_6E : LocalName = LocalName :: pack_static (368u32) ;
pub const ATOM_LOCALNAME__62_65_76_65_6C_6C_65_64 : LocalName = LocalName :: pack_static (369u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_73_69_7A_65 : LocalName = LocalName :: pack_static (370u32) ;
pub const ATOM_LOCALNAME__74_61_62_6C_65_56_61_6C_75_65_73 : LocalName = LocalName :: pack_static (371u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (372u32) ;
pub const ATOM_LOCALNAME__6D_74_65_78_74 : LocalName = LocalName :: pack_static (373u32) ;
pub const ATOM_LOCALNAME__76_6B_65_72_6E : LocalName = LocalName :: pack_static (374u32) ;
pub const ATOM_LOCALNAME__63_6C_6F_73_65 : LocalName = LocalName :: pack_static (375u32) ;
pub const ATOM_LOCALNAME__62_6F_64_79 : LocalName = LocalName :: pack_static (376u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_73_72_63 : LocalName = LocalName :: pack_static (377u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_44_75_72 : LocalName = LocalName :: pack_static (378u32) ;
pub const ATOM_LOCALNAME__6B_65_79_54_69_6D_65_73 : LocalName = LocalName :: pack_static (379u32) ;
pub const ATOM_LOCALNAME__73 : LocalName = LocalName :: pack_static (380u32) ;
pub const ATOM_LOCALNAME__76_61_6C_75_65 : LocalName = LocalName :: pack_static (381u32) ;
pub const ATOM_LOCALNAME__6D_75_6E_64_65_72_6F_76_65_72 : LocalName = LocalName :: pack_static (382u32) ;
pub const ATOM_LOCALNAME__66_65_43_6F_6C_6F_72_4D_61_74_72_69_78 : LocalName = LocalName :: pack_static (383u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_69_6F_6E : LocalName = LocalName :: pack_static (384u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_61_6C_69_67_6E : LocalName = LocalName :: pack_static (385u32) ;
pub const ATOM_LOCALNAME__73_74_72_69_6B_65 : LocalName = LocalName :: pack_static (386u32) ;
pub const ATOM_LOCALNAME__6D_6F : LocalName = LocalName :: pack_static (387u32) ;
pub const ATOM_LOCALNAME_ : LocalName = LocalName :: pack_static (388u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6D_75_6C_74_69_6C_69_6E_65 : LocalName = LocalName :: pack_static (389u32) ;
pub const ATOM_LOCALNAME__72_6F_6F_74 : LocalName = LocalName :: pack_static (390u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_73_74_79_6C_65 : LocalName = LocalName :: pack_static (391u32) ;
pub const ATOM_LOCALNAME__6D_73_75_62_73_75_70 : LocalName = LocalName :: pack_static (392u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74 : LocalName = LocalName :: pack_static (393u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_50_61_74_68 : LocalName = LocalName :: pack_static (394u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_65_6D_61_74_69_63_61_6C : LocalName = LocalName :: pack_static (395u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_50_72_6F_66_69_6C_65 : LocalName = LocalName :: pack_static (396u32) ;
pub const ATOM_LOCALNAME__61_63_74_75_61_74_65 : LocalName = LocalName :: pack_static (397u32) ;
pub const ATOM_LOCALNAME__74_79_70_65 : LocalName = LocalName :: pack_static (398u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72_2D_69_6E_74_65_72_70_6F_6C_61_74_69_6F_6E : LocalName = LocalName :: pack_static (399u32) ;
pub const ATOM_LOCALNAME__63_75_72_73_6F_72 : LocalName = LocalName :: pack_static (400u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_73_74_72_65_74_63_68 : LocalName = LocalName :: pack_static (401u32) ;
pub const ATOM_LOCALNAME__64_61_74_61 : LocalName = LocalName :: pack_static (402u32) ;
pub const ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_74_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (403u32) ;
pub const ATOM_LOCALNAME__72_71_75_6F_74_65 : LocalName = LocalName :: pack_static (404u32) ;
pub const ATOM_LOCALNAME__75_6E_69_63_6F_64_65_2D_62_69_64_69 : LocalName = LocalName :: pack_static (405u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6C_65_76_65_6C : LocalName = LocalName :: pack_static (406u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_61_67_65_68_69_64_65 : LocalName = LocalName :: pack_static (407u32) ;
pub const ATOM_LOCALNAME__73_74_72_65_74_63_68_79 : LocalName = LocalName :: pack_static (408u32) ;
pub const ATOM_LOCALNAME__64_69_61_6C_6F_67 : LocalName = LocalName :: pack_static (409u32) ;
pub const ATOM_LOCALNAME__65_6E_64 : LocalName = LocalName :: pack_static (410u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_75_72_69 : LocalName = LocalName :: pack_static (411u32) ;
pub const ATOM_LOCALNAME__63_6F_74_68 : LocalName = LocalName :: pack_static (412u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (413u32) ;
pub const ATOM_LOCALNAME__70_61_74_68_4C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (414u32) ;
pub const ATOM_LOCALNAME__6F_70_74_69_6F_6E : LocalName = LocalName :: pack_static (415u32) ;
pub const ATOM_LOCALNAME__63_61_6C_63_6D_6F_64_65 : LocalName = LocalName :: pack_static (416u32) ;
pub const ATOM_LOCALNAME__69_74_65_6D_74_79_70_65 : LocalName = LocalName :: pack_static (417u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68 : LocalName = LocalName :: pack_static (418u32) ;
pub const ATOM_LOCALNAME__74_61_62_69_6E_64_65_78 : LocalName = LocalName :: pack_static (419u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_65_6E_64 : LocalName = LocalName :: pack_static (420u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_2D_6D_61_78 : LocalName = LocalName :: pack_static (421u32) ;
pub const ATOM_LOCALNAME__62_62_6F_78 : LocalName = LocalName :: pack_static (422u32) ;
pub const ATOM_LOCALNAME__63_6F_73 : LocalName = LocalName :: pack_static (423u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_73_69_7A_65 : LocalName = LocalName :: pack_static (424u32) ;
pub const ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_2D_73_72_63 : LocalName = LocalName :: pack_static (425u32) ;
pub const ATOM_LOCALNAME__66_79 : LocalName = LocalName :: pack_static (426u32) ;
pub const ATOM_LOCALNAME__66_65_69_6D_61_67_65 : LocalName = LocalName :: pack_static (427u32) ;
pub const ATOM_LOCALNAME__6D_65_6E_75_69_74_65_6D : LocalName = LocalName :: pack_static (428u32) ;
pub const ATOM_LOCALNAME__63_68_65_63_6B_65_64 : LocalName = LocalName :: pack_static (429u32) ;
pub const ATOM_LOCALNAME__76_2D_61_6C_70_68_61_62_65_74_69_63 : LocalName = LocalName :: pack_static (430u32) ;
pub const ATOM_LOCALNAME__72_65_66_79 : LocalName = LocalName :: pack_static (431u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_6E_74 : LocalName = LocalName :: pack_static (432u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_63_75_73_69_6E : LocalName = LocalName :: pack_static (433u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_2D_6E_61_6D_65 : LocalName = LocalName :: pack_static (434u32) ;
pub const ATOM_LOCALNAME__73_75_70 : LocalName = LocalName :: pack_static (435u32) ;
pub const ATOM_LOCALNAME__62_64_69 : LocalName = LocalName :: pack_static (436u32) ;
pub const ATOM_LOCALNAME__61_72_63_63_6F_73 : LocalName = LocalName :: pack_static (437u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_64_69_74_69_6F_6E : LocalName = LocalName :: pack_static (438u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_72_76_61_6C : LocalName = LocalName :: pack_static (439u32) ;
pub const ATOM_LOCALNAME__6F_74_68_65_72 : LocalName = LocalName :: pack_static (440u32) ;
pub const ATOM_LOCALNAME__73_65_6D_61_6E_74_69_63_73 : LocalName = LocalName :: pack_static (441u32) ;
pub const ATOM_LOCALNAME__76_69_73_69_62_69_6C_69_74_79 : LocalName = LocalName :: pack_static (442u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_68_61_73_70_6F_70_75_70 : LocalName = LocalName :: pack_static (443u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_63_68_61_6E_6E_65_6C : LocalName = LocalName :: pack_static (444u32) ;
pub const ATOM_LOCALNAME__66_65_46_75_6E_63_42 : LocalName = LocalName :: pack_static (445u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_43_6F_75_6E_74 : LocalName = LocalName :: pack_static (446u32) ;
pub const ATOM_LOCALNAME__6F_62_6A_65_63_74 : LocalName = LocalName :: pack_static (447u32) ;
pub const ATOM_LOCALNAME__67_31 : LocalName = LocalName :: pack_static (448u32) ;
pub const ATOM_LOCALNAME__70_69 : LocalName = LocalName :: pack_static (449u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_7A : LocalName = LocalName :: pack_static (450u32) ;
pub const ATOM_LOCALNAME__68_69_64_64_65_6E : LocalName = LocalName :: pack_static (451u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_66_72_65_71_75_65_6E_63_79 : LocalName = LocalName :: pack_static (452u32) ;
pub const ATOM_LOCALNAME__69_64 : LocalName = LocalName :: pack_static (453u32) ;
pub const ATOM_LOCALNAME__73_74_61_72_74_6F_66_66_73_65_74 : LocalName = LocalName :: pack_static (454u32) ;
pub const ATOM_LOCALNAME__64_65_6C : LocalName = LocalName :: pack_static (455u32) ;
pub const ATOM_LOCALNAME__77_68_65_6E : LocalName = LocalName :: pack_static (456u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B_75_6E_69_74_73 : LocalName = LocalName :: pack_static (457u32) ;
pub const ATOM_LOCALNAME__65_78_70_6F_6E_65_6E_74_69_61_6C_65 : LocalName = LocalName :: pack_static (458u32) ;
pub const ATOM_LOCALNAME__6D_75_74_65_64 : LocalName = LocalName :: pack_static (459u32) ;
pub const ATOM_LOCALNAME__61_6E_6E_6F_74_61_74_69_6F_6E : LocalName = LocalName :: pack_static (460u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72 : LocalName = LocalName :: pack_static (461u32) ;
pub const ATOM_LOCALNAME__74_68_65_61_64 : LocalName = LocalName :: pack_static (462u32) ;
pub const ATOM_LOCALNAME__74_72 : LocalName = LocalName :: pack_static (463u32) ;
pub const ATOM_LOCALNAME__61_75_74_6F_66_6F_63_75_73 : LocalName = LocalName :: pack_static (464u32) ;
pub const ATOM_LOCALNAME__6C_69_6D_69_74_69_6E_67_63_6F_6E_65_61_6E_67_6C_65 : LocalName = LocalName :: pack_static (465u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_73_68_6F_77 : LocalName = LocalName :: pack_static (466u32) ;
pub const ATOM_LOCALNAME__69_74_65_6D_73_63_6F_70_65 : LocalName = LocalName :: pack_static (467u32) ;
pub const ATOM_LOCALNAME__70_6F_6C_79_6C_69_6E_65 : LocalName = LocalName :: pack_static (468u32) ;
pub const ATOM_LOCALNAME__6D_61_69_6E : LocalName = LocalName :: pack_static (469u32) ;
pub const ATOM_LOCALNAME__73_69_7A_65_73 : LocalName = LocalName :: pack_static (470u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65 : LocalName = LocalName :: pack_static (471u32) ;
pub const ATOM_LOCALNAME__64_79 : LocalName = LocalName :: pack_static (472u32) ;
pub const ATOM_LOCALNAME__6D_61_63_74_69_6F_6E : LocalName = LocalName :: pack_static (473u32) ;
pub const ATOM_LOCALNAME__6C_6F_6F_70 : LocalName = LocalName :: pack_static (474u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_65_73_73_61_67_65 : LocalName = LocalName :: pack_static (475u32) ;
pub const ATOM_LOCALNAME__65_64_67_65_4D_6F_64_65 : LocalName = LocalName :: pack_static (476u32) ;
pub const ATOM_LOCALNAME__61_72_63_63_6F_74_68 : LocalName = LocalName :: pack_static (477u32) ;
pub const ATOM_LOCALNAME__66_69_6C_6C_2D_72_75_6C_65 : LocalName = LocalName :: pack_static (478u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_53_63_72_69_70_74_54_79_70_65 : LocalName = LocalName :: pack_static (479u32) ;
pub const ATOM_LOCALNAME__65_78_69_73_74_73 : LocalName = LocalName :: pack_static (480u32) ;
pub const ATOM_LOCALNAME__67_74 : LocalName = LocalName :: pack_static (481u32) ;
pub const ATOM_LOCALNAME__63_6C_6F_73_75_72_65 : LocalName = LocalName :: pack_static (482u32) ;
pub const ATOM_LOCALNAME__64_61_74_61_74_65_6D_70_6C_61_74_65 : LocalName = LocalName :: pack_static (483u32) ;
pub const ATOM_LOCALNAME__74_61_6E_68 : LocalName = LocalName :: pack_static (484u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_70_61_74_68_75_6E_69_74_73 : LocalName = LocalName :: pack_static (485u32) ;
pub const ATOM_LOCALNAME__70_72_65_6C_6F_61_64 : LocalName = LocalName :: pack_static (486u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_72_6F_6C_73 : LocalName = LocalName :: pack_static (487u32) ;
pub const ATOM_LOCALNAME__68_72_65_66 : LocalName = LocalName :: pack_static (488u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_45_78_70_6F_6E_65_6E_74 : LocalName = LocalName :: pack_static (489u32) ;
pub const ATOM_LOCALNAME__69_74_65_6D_72_65_66 : LocalName = LocalName :: pack_static (490u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_74_79_70_65 : LocalName = LocalName :: pack_static (491u32) ;
pub const ATOM_LOCALNAME__6C_6F_77_6C_69_6D_69_74 : LocalName = LocalName :: pack_static (492u32) ;
pub const ATOM_LOCALNAME__64_65_66_73 : LocalName = LocalName :: pack_static (493u32) ;
pub const ATOM_LOCALNAME__75_6C : LocalName = LocalName :: pack_static (494u32) ;
pub const ATOM_LOCALNAME__76_65_72_74_2D_6F_72_69_67_69_6E_2D_78 : LocalName = LocalName :: pack_static (495u32) ;
pub const ATOM_LOCALNAME__6C_69_67_68_74_69_6E_67_2D_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (496u32) ;
pub const ATOM_LOCALNAME__61_72_63_73_65_63 : LocalName = LocalName :: pack_static (497u32) ;
pub const ATOM_LOCALNAME__64_65_73_63 : LocalName = LocalName :: pack_static (498u32) ;
pub const ATOM_LOCALNAME__6F_72_69_67_69_6E : LocalName = LocalName :: pack_static (499u32) ;
pub const ATOM_LOCALNAME__76_65_72_79_76_65_72_79_74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (500u32) ;
pub const ATOM_LOCALNAME__72_6F_77_73 : LocalName = LocalName :: pack_static (501u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_2D_6D_69_6E : LocalName = LocalName :: pack_static (502u32) ;
pub const ATOM_LOCALNAME__68_69_67_68 : LocalName = LocalName :: pack_static (503u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_61_74 : LocalName = LocalName :: pack_static (504u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_73_75_62_73_65_74 : LocalName = LocalName :: pack_static (505u32) ;
pub const ATOM_LOCALNAME__73_63_61_6C_65 : LocalName = LocalName :: pack_static (506u32) ;
pub const ATOM_LOCALNAME__61_72_63_73_69_6E_68 : LocalName = LocalName :: pack_static (507u32) ;
pub const ATOM_LOCALNAME__61_72_63_72_6F_6C_65 : LocalName = LocalName :: pack_static (508u32) ;
pub const ATOM_LOCALNAME__6D_72_6F_77 : LocalName = LocalName :: pack_static (509u32) ;
pub const ATOM_LOCALNAME__69_6D_61_67_65_2D_72_65_6E_64_65_72_69_6E_67 : LocalName = LocalName :: pack_static (510u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_69_6C_74_65_72_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (511u32) ;
pub const ATOM_LOCALNAME__66_65_53_70_6F_74_4C_69_67_68_74 : LocalName = LocalName :: pack_static (512u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65 : LocalName = LocalName :: pack_static (513u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_63_6F_6E_73_74_61_6E_74 : LocalName = LocalName :: pack_static (514u32) ;
pub const ATOM_LOCALNAME__6C_61_72_67_65_6F_70 : LocalName = LocalName :: pack_static (515u32) ;
pub const ATOM_LOCALNAME__66_69_6C_6C : LocalName = LocalName :: pack_static (516u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_76_65 : LocalName = LocalName :: pack_static (517u32) ;
pub const ATOM_LOCALNAME__6F_6C : LocalName = LocalName :: pack_static (518u32) ;
pub const ATOM_LOCALNAME__73_6F_75_72_63_65 : LocalName = LocalName :: pack_static (519u32) ;
pub const ATOM_LOCALNAME__6F_6E_61_62_6F_72_74 : LocalName = LocalName :: pack_static (520u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_63_6F_6E_74_72_6F_6C_73 : LocalName = LocalName :: pack_static (521u32) ;
pub const ATOM_LOCALNAME__78_63_68_61_6E_6E_65_6C_73_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (522u32) ;
pub const ATOM_LOCALNAME__73_79_6D_62_6F_6C : LocalName = LocalName :: pack_static (523u32) ;
pub const ATOM_LOCALNAME__71_75_6F_74_69_65_6E_74 : LocalName = LocalName :: pack_static (524u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_61_72_67_72_61_64_69_65_6E_74 : LocalName = LocalName :: pack_static (525u32) ;
pub const ATOM_LOCALNAME__72_62 : LocalName = LocalName :: pack_static (526u32) ;
pub const ATOM_LOCALNAME__74_6F_67_67_6C_65 : LocalName = LocalName :: pack_static (527u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72_2D_69_6E_74_65_72_70_6F_6C_61_74_69_6F_6E_2D_66_69_6C_74_65_72_73 : LocalName = LocalName :: pack_static (528u32) ;
pub const ATOM_LOCALNAME__64_65_66_61_75_6C_74 : LocalName = LocalName :: pack_static (529u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_73_73_6B_65_79 : LocalName = LocalName :: pack_static (530u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_73_69_7A_65 : LocalName = LocalName :: pack_static (531u32) ;
pub const ATOM_LOCALNAME__6D_70_68_61_6E_74_6F_6D : LocalName = LocalName :: pack_static (532u32) ;
pub const ATOM_LOCALNAME__74_61_62_6C_65_76_61_6C_75_65_73 : LocalName = LocalName :: pack_static (533u32) ;
pub const ATOM_LOCALNAME__73_74_65_6D_68 : LocalName = LocalName :: pack_static (534u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_64_65_61_63_74_69_76_61_74_65 : LocalName = LocalName :: pack_static (535u32) ;
pub const ATOM_LOCALNAME__6F_6E_75_6E_6C_6F_61_64 : LocalName = LocalName :: pack_static (536u32) ;
pub const ATOM_LOCALNAME__66_65_46_75_6E_63_41 : LocalName = LocalName :: pack_static (537u32) ;
pub const ATOM_LOCALNAME__73_65_63 : LocalName = LocalName :: pack_static (538u32) ;
pub const ATOM_LOCALNAME__76_69_65_77_54_61_72_67_65_74 : LocalName = LocalName :: pack_static (539u32) ;
pub const ATOM_LOCALNAME__76_69_65_77 : LocalName = LocalName :: pack_static (540u32) ;
pub const ATOM_LOCALNAME__73_72_63_73_65_74 : LocalName = LocalName :: pack_static (541u32) ;
pub const ATOM_LOCALNAME__70_6F_73_74_65_72 : LocalName = LocalName :: pack_static (542u32) ;
pub const ATOM_LOCALNAME__61_75_64_69_6F : LocalName = LocalName :: pack_static (543u32) ;
pub const ATOM_LOCALNAME__6F_72_69_65_6E_74_61_74_69_6F_6E : LocalName = LocalName :: pack_static (544u32) ;
pub const ATOM_LOCALNAME__76_65_63_74_6F_72 : LocalName = LocalName :: pack_static (545u32) ;
pub const ATOM_LOCALNAME__61_6D_70_6C_69_74_75_64_65 : LocalName = LocalName :: pack_static (546u32) ;
pub const ATOM_LOCALNAME__61 : LocalName = LocalName :: pack_static (547u32) ;
pub const ATOM_LOCALNAME__72_75_6C_65_73 : LocalName = LocalName :: pack_static (548u32) ;
pub const ATOM_LOCALNAME__70_6C_61_63_65_68_6F_6C_64_65_72 : LocalName = LocalName :: pack_static (549u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_2D_70_61_74_68 : LocalName = LocalName :: pack_static (550u32) ;
pub const ATOM_LOCALNAME__66_65_47_61_75_73_73_69_61_6E_42_6C_75_72 : LocalName = LocalName :: pack_static (551u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_6A_75_67_61_74_65 : LocalName = LocalName :: pack_static (552u32) ;
pub const ATOM_LOCALNAME__70_6F_6C_79_67_6F_6E : LocalName = LocalName :: pack_static (553u32) ;
pub const ATOM_LOCALNAME__6D_72_6F_6F_74 : LocalName = LocalName :: pack_static (554u32) ;
pub const ATOM_LOCALNAME__61_72_63_73_65_63_68 : LocalName = LocalName :: pack_static (555u32) ;
pub const ATOM_LOCALNAME__6D_61_6C_69_67_6E_67_72_6F_75_70 : LocalName = LocalName :: pack_static (556u32) ;
pub const ATOM_LOCALNAME__69_6E : LocalName = LocalName :: pack_static (557u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72_2D_70_72_6F_66_69_6C_65 : LocalName = LocalName :: pack_static (558u32) ;
pub const ATOM_LOCALNAME__73_74_61_6E_64_62_79 : LocalName = LocalName :: pack_static (559u32) ;
pub const ATOM_LOCALNAME__72_79 : LocalName = LocalName :: pack_static (560u32) ;
pub const ATOM_LOCALNAME__73_70_61_6E : LocalName = LocalName :: pack_static (561u32) ;
pub const ATOM_LOCALNAME__6E_6F_62_72 : LocalName = LocalName :: pack_static (562u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_65_78_70_6F_6E_65_6E_74 : LocalName = LocalName :: pack_static (563u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_6E_74_75_6E_64_65_72 : LocalName = LocalName :: pack_static (564u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_64_69_73_61_62_6C_65_64 : LocalName = LocalName :: pack_static (565u32) ;
pub const ATOM_LOCALNAME__72_75_62_79 : LocalName = LocalName :: pack_static (566u32) ;
pub const ATOM_LOCALNAME__64_64 : LocalName = LocalName :: pack_static (567u32) ;
pub const ATOM_LOCALNAME__69_6D_61_67_69_6E_61_72_79 : LocalName = LocalName :: pack_static (568u32) ;
pub const ATOM_LOCALNAME__61_72_65_61 : LocalName = LocalName :: pack_static (569u32) ;
pub const ATOM_LOCALNAME__76_65_72_79_74_68_69_6E_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (570u32) ;
pub const ATOM_LOCALNAME__66_65_6E_63_65 : LocalName = LocalName :: pack_static (571u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_43_6F_6E_74_65_6E_74_55_6E_69_74_73 : LocalName = LocalName :: pack_static (572u32) ;
pub const ATOM_LOCALNAME__6F_72_64_65_72 : LocalName = LocalName :: pack_static (573u32) ;
pub const ATOM_LOCALNAME__65_78_70 : LocalName = LocalName :: pack_static (574u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_65_64_69_74_66_6F_63_75_73 : LocalName = LocalName :: pack_static (575u32) ;
pub const ATOM_LOCALNAME__65_6E_63_74_79_70_65 : LocalName = LocalName :: pack_static (576u32) ;
pub const ATOM_LOCALNAME__73_65_70 : LocalName = LocalName :: pack_static (577u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64_66_65_61_74_75_72_65_73 : LocalName = LocalName :: pack_static (578u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_4D_6F_74_69_6F_6E : LocalName = LocalName :: pack_static (579u32) ;
pub const ATOM_LOCALNAME__72 : LocalName = LocalName :: pack_static (580u32) ;
pub const ATOM_LOCALNAME__69_74_65_6D_69_64 : LocalName = LocalName :: pack_static (581u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_61_72_63_72_6F_6C_65 : LocalName = LocalName :: pack_static (582u32) ;
pub const ATOM_LOCALNAME__66_65_64_72_6F_70_73_68_61_64_6F_77 : LocalName = LocalName :: pack_static (583u32) ;
pub const ATOM_LOCALNAME__66_65_74_63_68 : LocalName = LocalName :: pack_static (584u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_2D_64_65_63_6F_72_61_74_69_6F_6E : LocalName = LocalName :: pack_static (585u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_63_75_73 : LocalName = LocalName :: pack_static (586u32) ;
pub const ATOM_LOCALNAME__76_65_72_74_2D_61_64_76_2D_79 : LocalName = LocalName :: pack_static (587u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_74_61_72_67_65_74 : LocalName = LocalName :: pack_static (588u32) ;
pub const ATOM_LOCALNAME__62_6C_69_6E_6B : LocalName = LocalName :: pack_static (589u32) ;
pub const ATOM_LOCALNAME__73_63_6F_70_65_64 : LocalName = LocalName :: pack_static (590u32) ;
pub const ATOM_LOCALNAME__6D_6F_6D_65_6E_74 : LocalName = LocalName :: pack_static (591u32) ;
pub const ATOM_LOCALNAME__6D_65_74_68_6F_64 : LocalName = LocalName :: pack_static (592u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72_55_6E_69_74_73 : LocalName = LocalName :: pack_static (593u32) ;
pub const ATOM_LOCALNAME__6D_65_61_6E : LocalName = LocalName :: pack_static (594u32) ;
pub const ATOM_LOCALNAME__6C_63_6D : LocalName = LocalName :: pack_static (595u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6E_67 : LocalName = LocalName :: pack_static (596u32) ;
pub const ATOM_LOCALNAME__65_71_75_61_6C_72_6F_77_73 : LocalName = LocalName :: pack_static (597u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_65_6C_65_63_74_73_74_61_72_74 : LocalName = LocalName :: pack_static (598u32) ;
pub const ATOM_LOCALNAME__6C_6F_63_61_6C : LocalName = LocalName :: pack_static (599u32) ;
pub const ATOM_LOCALNAME__66_65_44_72_6F_70_53_68_61_64_6F_77 : LocalName = LocalName :: pack_static (600u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_64_6F_77_6E : LocalName = LocalName :: pack_static (601u32) ;
pub const ATOM_LOCALNAME__67_63_64 : LocalName = LocalName :: pack_static (602u32) ;
pub const ATOM_LOCALNAME__6D_75_6C_74_69_63_6F_6C : LocalName = LocalName :: pack_static (603u32) ;
pub const ATOM_LOCALNAME__68_36 : LocalName = LocalName :: pack_static (604u32) ;
pub const ATOM_LOCALNAME__73_74_6F_70 : LocalName = LocalName :: pack_static (605u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_6F_70 : LocalName = LocalName :: pack_static (606u32) ;
pub const ATOM_LOCALNAME__61_72_63_63_73_63 : LocalName = LocalName :: pack_static (607u32) ;
pub const ATOM_LOCALNAME__6F_6E_7A_6F_6F_6D : LocalName = LocalName :: pack_static (608u32) ;
pub const ATOM_LOCALNAME__63_6C_65_61_72 : LocalName = LocalName :: pack_static (609u32) ;
pub const ATOM_LOCALNAME__73_75_62 : LocalName = LocalName :: pack_static (610u32) ;
pub const ATOM_LOCALNAME__69_74_65_6D_70_72_6F_70 : LocalName = LocalName :: pack_static (611u32) ;
pub const ATOM_LOCALNAME__73_63_61_6C_61_72_70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (612u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_75_62_6D_69_74 : LocalName = LocalName :: pack_static (613u32) ;
pub const ATOM_LOCALNAME__66_65_44_69_73_70_6C_61_63_65_6D_65_6E_74_4D_61_70 : LocalName = LocalName :: pack_static (614u32) ;
pub const ATOM_LOCALNAME__66_65_54_75_72_62_75_6C_65_6E_63_65 : LocalName = LocalName :: pack_static (615u32) ;
pub const ATOM_LOCALNAME__6F_75_74_65_72_70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (616u32) ;
pub const ATOM_LOCALNAME__61_6C_6C_6F_77_66_75_6C_6C_73_63_72_65_65_6E : LocalName = LocalName :: pack_static (617u32) ;
pub const ATOM_LOCALNAME__6C_61_62_65_6C : LocalName = LocalName :: pack_static (618u32) ;
pub const ATOM_LOCALNAME__73_64_65_76 : LocalName = LocalName :: pack_static (619u32) ;
pub const ATOM_LOCALNAME__65_78_70_6F_6E_65_6E_74 : LocalName = LocalName :: pack_static (620u32) ;
pub const ATOM_LOCALNAME__73_70_65_65_64 : LocalName = LocalName :: pack_static (621u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_6E_61_6D_65 : LocalName = LocalName :: pack_static (622u32) ;
pub const ATOM_LOCALNAME__64_65_73_63_65_6E_74 : LocalName = LocalName :: pack_static (623u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74 : LocalName = LocalName :: pack_static (624u32) ;
pub const ATOM_LOCALNAME__61_70_70_6C_65_74 : LocalName = LocalName :: pack_static (625u32) ;
pub const ATOM_LOCALNAME__66_65_74_75_72_62_75_6C_65_6E_63_65 : LocalName = LocalName :: pack_static (626u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_66_61_6D_69_6C_79 : LocalName = LocalName :: pack_static (627u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E : LocalName = LocalName :: pack_static (628u32) ;
pub const ATOM_LOCALNAME__61_72_74_69_63_6C_65 : LocalName = LocalName :: pack_static (629u32) ;
pub const ATOM_LOCALNAME__61_63_74_69_76_65 : LocalName = LocalName :: pack_static (630u32) ;
pub const ATOM_LOCALNAME__72_65_66_65_72_72_65_72_70_6F_6C_69_63_79 : LocalName = LocalName :: pack_static (631u32) ;
pub const ATOM_LOCALNAME__72_65_66_78 : LocalName = LocalName :: pack_static (632u32) ;
pub const ATOM_LOCALNAME__76_65_63_74_6F_72_70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (633u32) ;
pub const ATOM_LOCALNAME__63_61_72_64 : LocalName = LocalName :: pack_static (634u32) ;
pub const ATOM_LOCALNAME__72_6F_74_61_74_65 : LocalName = LocalName :: pack_static (635u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_6F_6E_74_72_6F_6C_73_65_6C_65_63_74 : LocalName = LocalName :: pack_static (636u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B_55_6E_69_74_73 : LocalName = LocalName :: pack_static (637u32) ;
pub const ATOM_LOCALNAME__72_65_76 : LocalName = LocalName :: pack_static (638u32) ;
pub const ATOM_LOCALNAME__66_65_66_75_6E_63_61 : LocalName = LocalName :: pack_static (639u32) ;
pub const ATOM_LOCALNAME__72_65_6E_64_65_72_69_6E_67_2D_69_6E_74_65_6E_74 : LocalName = LocalName :: pack_static (640u32) ;
pub const ATOM_LOCALNAME__75_6E_69_63_6F_64_65 : LocalName = LocalName :: pack_static (641u32) ;
pub const ATOM_LOCALNAME__69_73_6D_61_70 : LocalName = LocalName :: pack_static (642u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_65_73_65_74 : LocalName = LocalName :: pack_static (643u32) ;
pub const ATOM_LOCALNAME__73_61_6D_70 : LocalName = LocalName :: pack_static (644u32) ;
pub const ATOM_LOCALNAME__65_6D_70_74_79_73_65_74 : LocalName = LocalName :: pack_static (645u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_65_6E_74_65_72 : LocalName = LocalName :: pack_static (646u32) ;
pub const ATOM_LOCALNAME__65_76_65_6E_74 : LocalName = LocalName :: pack_static (647u32) ;
pub const ATOM_LOCALNAME__6C_71_75_6F_74_65 : LocalName = LocalName :: pack_static (648u32) ;
pub const ATOM_LOCALNAME__68_74_6D_6C : LocalName = LocalName :: pack_static (649u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_77_69_64_74_68 : LocalName = LocalName :: pack_static (650u32) ;
pub const ATOM_LOCALNAME__6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (651u32) ;
pub const ATOM_LOCALNAME__64_65_63_6F_64_69_6E_67 : LocalName = LocalName :: pack_static (652u32) ;
pub const ATOM_LOCALNAME__6E_65_73_74 : LocalName = LocalName :: pack_static (653u32) ;
pub const ATOM_LOCALNAME__6F_66_66_73_65_74 : LocalName = LocalName :: pack_static (654u32) ;
pub const ATOM_LOCALNAME__73_79_73_74_65_6D_4C_61_6E_67_75_61_67_65 : LocalName = LocalName :: pack_static (655u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_73 : LocalName = LocalName :: pack_static (656u32) ;
pub const ATOM_LOCALNAME__6E_61_74_75_72_61_6C_6E_75_6D_62_65_72_73 : LocalName = LocalName :: pack_static (657u32) ;
pub const ATOM_LOCALNAME__6D_74_61_62_6C_65 : LocalName = LocalName :: pack_static (658u32) ;
pub const ATOM_LOCALNAME__76_61_6C_75_65_74_79_70_65 : LocalName = LocalName :: pack_static (659u32) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74 : LocalName = LocalName :: pack_static (660u32) ;
pub const ATOM_LOCALNAME__6D_61_78_73_69_7A_65 : LocalName = LocalName :: pack_static (661u32) ;
pub const ATOM_LOCALNAME__6E_6F_73_63_72_69_70_74 : LocalName = LocalName :: pack_static (662u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_70_6F_73_69_6E_73_65_74 : LocalName = LocalName :: pack_static (663u32) ;
pub const ATOM_LOCALNAME__73_74_79_6C_65 : LocalName = LocalName :: pack_static (664u32) ;
pub const ATOM_LOCALNAME__69_6E_70_75_74_6D_6F_64_65 : LocalName = LocalName :: pack_static (665u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_67_72_61_62 : LocalName = LocalName :: pack_static (666u32) ;
pub const ATOM_LOCALNAME__77_72_69_74_69_6E_67_2D_6D_6F_64_65 : LocalName = LocalName :: pack_static (667u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_64_61_74_61_74_79_70_65 : LocalName = LocalName :: pack_static (668u32) ;
pub const ATOM_LOCALNAME__66_72_61_6D_65 : LocalName = LocalName :: pack_static (669u32) ;
pub const ATOM_LOCALNAME__64_78 : LocalName = LocalName :: pack_static (670u32) ;
pub const ATOM_LOCALNAME__78_32 : LocalName = LocalName :: pack_static (671u32) ;
pub const ATOM_LOCALNAME__72_65_61_6C : LocalName = LocalName :: pack_static (672u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65 : LocalName = LocalName :: pack_static (673u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_61_73_74_65 : LocalName = LocalName :: pack_static (674u32) ;
pub const ATOM_LOCALNAME__6D_70_61_74_68 : LocalName = LocalName :: pack_static (675u32) ;
pub const ATOM_LOCALNAME__6D_65_64_69_75_6D_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (676u32) ;
pub const ATOM_LOCALNAME__69_64_65_6E_74 : LocalName = LocalName :: pack_static (677u32) ;
pub const ATOM_LOCALNAME__64_61_74_65_74_69_6D_65 : LocalName = LocalName :: pack_static (678u32) ;
pub const ATOM_LOCALNAME__64_69_76 : LocalName = LocalName :: pack_static (679u32) ;
pub const ATOM_LOCALNAME__65_75_6C_65_72_67_61_6D_6D_61 : LocalName = LocalName :: pack_static (680u32) ;
pub const ATOM_LOCALNAME__63_73_63 : LocalName = LocalName :: pack_static (681u32) ;
pub const ATOM_LOCALNAME__6B : LocalName = LocalName :: pack_static (682u32) ;
pub const ATOM_LOCALNAME__66_61_6C_73_65 : LocalName = LocalName :: pack_static (683u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_6E_74_2D_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (684u32) ;
pub const ATOM_LOCALNAME__68_72_65_66_6C_61_6E_67 : LocalName = LocalName :: pack_static (685u32) ;
pub const ATOM_LOCALNAME__6B_32 : LocalName = LocalName :: pack_static (686u32) ;
pub const ATOM_LOCALNAME__76_69_65_77_74_61_72_67_65_74 : LocalName = LocalName :: pack_static (687u32) ;
pub const ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_54_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (688u32) ;
pub const ATOM_LOCALNAME__73_65_70_61_72_61_74_6F_72 : LocalName = LocalName :: pack_static (689u32) ;
pub const ATOM_LOCALNAME__64_65_66_65_72 : LocalName = LocalName :: pack_static (690u32) ;
pub const ATOM_LOCALNAME__66_72_6F_6D : LocalName = LocalName :: pack_static (691u32) ;
pub const ATOM_LOCALNAME__64_61_74_61_66_6C_64 : LocalName = LocalName :: pack_static (692u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_2D_6F_72_69_65_6E_74_61_74_69_6F_6E_2D_68_6F_72_69_7A_6F_6E_74_61_6C : LocalName = LocalName :: pack_static (693u32) ;
pub const ATOM_LOCALNAME__6E_75_6D_4F_63_74_61_76_65_73 : LocalName = LocalName :: pack_static (694u32) ;
pub const ATOM_LOCALNAME__6D_6F_76_65_72 : LocalName = LocalName :: pack_static (695u32) ;
pub const ATOM_LOCALNAME__6E_6F_77_72_61_70 : LocalName = LocalName :: pack_static (696u32) ;
pub const ATOM_LOCALNAME__72_61_64_69_61_6C_47_72_61_64_69_65_6E_74 : LocalName = LocalName :: pack_static (697u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_69_6E : LocalName = LocalName :: pack_static (698u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_73_69_7A_65_2D_61_64_6A_75_73_74 : LocalName = LocalName :: pack_static (699u32) ;
pub const ATOM_LOCALNAME__63_65_6C_6C_70_61_64_64_69_6E_67 : LocalName = LocalName :: pack_static (700u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_55_6E_69_74_73 : LocalName = LocalName :: pack_static (701u32) ;
pub const ATOM_LOCALNAME__64_74 : LocalName = LocalName :: pack_static (702u32) ;
pub const ATOM_LOCALNAME__66_6C_6F_6F_72 : LocalName = LocalName :: pack_static (703u32) ;
pub const ATOM_LOCALNAME__73_74_6F_70_2D_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (704u32) ;
pub const ATOM_LOCALNAME__66_6F_6F_74_65_72 : LocalName = LocalName :: pack_static (705u32) ;
pub const ATOM_LOCALNAME__73_65_6C_65_63_74_69_6F_6E : LocalName = LocalName :: pack_static (706u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_64_61_73_68_6F_66_66_73_65_74 : LocalName = LocalName :: pack_static (707u32) ;
pub const ATOM_LOCALNAME__70_72_6F_70_65_72_74_79 : LocalName = LocalName :: pack_static (708u32) ;
pub const ATOM_LOCALNAME__70_72_69_6D_65_73 : LocalName = LocalName :: pack_static (709u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_74_6F_72_61_67_65 : LocalName = LocalName :: pack_static (710u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_75_70 : LocalName = LocalName :: pack_static (711u32) ;
pub const ATOM_LOCALNAME__6D_65_64_69_61 : LocalName = LocalName :: pack_static (712u32) ;
pub const ATOM_LOCALNAME__73_75_6D_6D_61_72_79 : LocalName = LocalName :: pack_static (713u32) ;
pub const ATOM_LOCALNAME__6D_6F_64_65 : LocalName = LocalName :: pack_static (714u32) ;
pub const ATOM_LOCALNAME__6E_61_72_67_73 : LocalName = LocalName :: pack_static (715u32) ;
pub const ATOM_LOCALNAME__6E_6F_76_61_6C_69_64_61_74_65 : LocalName = LocalName :: pack_static (716u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_67_72_69_74_79 : LocalName = LocalName :: pack_static (717u32) ;
pub const ATOM_LOCALNAME__64_6F_6D_69_6E_61_6E_74_2D_62_61_73_65_6C_69_6E_65 : LocalName = LocalName :: pack_static (718u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (719u32) ;
pub const ATOM_LOCALNAME__69_63_6F_6E : LocalName = LocalName :: pack_static (720u32) ;
pub const ATOM_LOCALNAME__6F_70_65_6E : LocalName = LocalName :: pack_static (721u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_61_75_74_6F_63_6F_6D_70_6C_65_74_65 : LocalName = LocalName :: pack_static (722u32) ;
pub const ATOM_LOCALNAME__6F_70_74_67_72_6F_75_70 : LocalName = LocalName :: pack_static (723u32) ;
pub const ATOM_LOCALNAME__64_66_6E : LocalName = LocalName :: pack_static (724u32) ;
pub const ATOM_LOCALNAME__61_63_72_6F_6E_79_6D : LocalName = LocalName :: pack_static (725u32) ;
pub const ATOM_LOCALNAME__61_6C_69_67_6E : LocalName = LocalName :: pack_static (726u32) ;
pub const ATOM_LOCALNAME__72_61_64_69_6F_67_72_6F_75_70 : LocalName = LocalName :: pack_static (727u32) ;
pub const ATOM_LOCALNAME__74_72_65_66 : LocalName = LocalName :: pack_static (728u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_70_72_65_73_73_65_64 : LocalName = LocalName :: pack_static (729u32) ;
pub const ATOM_LOCALNAME__74_72_75_65 : LocalName = LocalName :: pack_static (730u32) ;
pub const ATOM_LOCALNAME__68_6B_65_72_6E : LocalName = LocalName :: pack_static (731u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_67_65_72_73 : LocalName = LocalName :: pack_static (732u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_66_6C_6F_77_74_6F : LocalName = LocalName :: pack_static (733u32) ;
pub const ATOM_LOCALNAME__72_65_6C : LocalName = LocalName :: pack_static (734u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_65_64_69_74_61_62_6C_65 : LocalName = LocalName :: pack_static (735u32) ;
pub const ATOM_LOCALNAME__72_74 : LocalName = LocalName :: pack_static (736u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_65_78_70_61_6E_64_65_64 : LocalName = LocalName :: pack_static (737u32) ;
pub const ATOM_LOCALNAME__74_73_70_61_6E : LocalName = LocalName :: pack_static (738u32) ;
pub const ATOM_LOCALNAME__66_65_64_69_73_70_6C_61_63_65_6D_65_6E_74_6D_61_70 : LocalName = LocalName :: pack_static (739u32) ;
pub const ATOM_LOCALNAME__6B_34 : LocalName = LocalName :: pack_static (740u32) ;
pub const ATOM_LOCALNAME__74_6F : LocalName = LocalName :: pack_static (741u32) ;
pub const ATOM_LOCALNAME__66_6F_72_65_69_67_6E_6F_62_6A_65_63_74 : LocalName = LocalName :: pack_static (742u32) ;
pub const ATOM_LOCALNAME__70_61_72_73_65 : LocalName = LocalName :: pack_static (743u32) ;
pub const ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_55_52_4C : LocalName = LocalName :: pack_static (744u32) ;
pub const ATOM_LOCALNAME__6D_65_6E_63_6C_6F_73_65 : LocalName = LocalName :: pack_static (745u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_65_6E_74_65_72 : LocalName = LocalName :: pack_static (746u32) ;
pub const ATOM_LOCALNAME__61_72_63_68_69_76_65 : LocalName = LocalName :: pack_static (747u32) ;
pub const ATOM_LOCALNAME__68_72 : LocalName = LocalName :: pack_static (748u32) ;
pub const ATOM_LOCALNAME__63_6F_6D_70_6F_73_65 : LocalName = LocalName :: pack_static (749u32) ;
pub const ATOM_LOCALNAME__66_69_6C_6C_2D_6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (750u32) ;
pub const ATOM_LOCALNAME__6D_6E : LocalName = LocalName :: pack_static (751u32) ;
pub const ATOM_LOCALNAME__73_65_63_68 : LocalName = LocalName :: pack_static (752u32) ;
pub const ATOM_LOCALNAME__61_64_64_69_74_69_76_65 : LocalName = LocalName :: pack_static (753u32) ;
pub const ATOM_LOCALNAME__6D_73_75_62 : LocalName = LocalName :: pack_static (754u32) ;
pub const ATOM_LOCALNAME__68_6F_72_69_7A_2D_6F_72_69_67_69_6E_2D_79 : LocalName = LocalName :: pack_static (755u32) ;
pub const ATOM_LOCALNAME__62_6F_72_64_65_72 : LocalName = LocalName :: pack_static (756u32) ;
pub const ATOM_LOCALNAME__62_69_61_73 : LocalName = LocalName :: pack_static (757u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_66_6F_72_6D_61_74 : LocalName = LocalName :: pack_static (758u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_70_74_2D_63_68_61_72_73_65_74 : LocalName = LocalName :: pack_static (759u32) ;
pub const ATOM_LOCALNAME__66_72_61_6D_65_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (760u32) ;
pub const ATOM_LOCALNAME__66_65_70_6F_69_6E_74_6C_69_67_68_74 : LocalName = LocalName :: pack_static (761u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64_46_65_61_74_75_72_65_73 : LocalName = LocalName :: pack_static (762u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_50_61_74_68 : LocalName = LocalName :: pack_static (763u32) ;
pub const ATOM_LOCALNAME__66_65_43_6F_6D_70_6F_73_69_74_65 : LocalName = LocalName :: pack_static (764u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_6D_6F_74_69_6F_6E : LocalName = LocalName :: pack_static (765u32) ;
pub const ATOM_LOCALNAME__6C_74 : LocalName = LocalName :: pack_static (766u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_75_6E_6C_6F_61_64 : LocalName = LocalName :: pack_static (767u32) ;
pub const ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68 : LocalName = LocalName :: pack_static (768u32) ;
pub const ATOM_LOCALNAME__72_6F_6C_65 : LocalName = LocalName :: pack_static (769u32) ;
pub const ATOM_LOCALNAME__66_65_4D_65_72_67_65 : LocalName = LocalName :: pack_static (770u32) ;
pub const ATOM_LOCALNAME__65_64_67_65 : LocalName = LocalName :: pack_static (771u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_72_6D_69_6E_70_75_74 : LocalName = LocalName :: pack_static (772u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_59 : LocalName = LocalName :: pack_static (773u32) ;
pub const ATOM_LOCALNAME__6C_69 : LocalName = LocalName :: pack_static (774u32) ;
pub const ATOM_LOCALNAME__76_73_70_61_63_65 : LocalName = LocalName :: pack_static (775u32) ;
pub const ATOM_LOCALNAME__6D_75_6C_74_69_70_6C_65 : LocalName = LocalName :: pack_static (776u32) ;
pub const ATOM_LOCALNAME__6D_65_72_72_6F_72 : LocalName = LocalName :: pack_static (777u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_61_63_74_69_76_65_64_65_73_63_65_6E_64_61_6E_74 : LocalName = LocalName :: pack_static (778u32) ;
pub const ATOM_LOCALNAME__6F_6E_68_61_73_68_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (779u32) ;
pub const ATOM_LOCALNAME__70_72_73_75_62_73_65_74 : LocalName = LocalName :: pack_static (780u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_6C_69_6E_65 : LocalName = LocalName :: pack_static (781u32) ;
pub const ATOM_LOCALNAME__74_64 : LocalName = LocalName :: pack_static (782u32) ;
pub const ATOM_LOCALNAME__72_70 : LocalName = LocalName :: pack_static (783u32) ;
pub const ATOM_LOCALNAME__66_61_63_74_6F_72_6F_66 : LocalName = LocalName :: pack_static (784u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_6F_6E_74_65_78_74_6D_65_6E_75 : LocalName = LocalName :: pack_static (785u32) ;
pub const ATOM_LOCALNAME__6C_6F_77 : LocalName = LocalName :: pack_static (786u32) ;
pub const ATOM_LOCALNAME__64_72_61_67_67_61_62_6C_65 : LocalName = LocalName :: pack_static (787u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_6F_70_73_74_61_74_65 : LocalName = LocalName :: pack_static (788u32) ;
pub const ATOM_LOCALNAME__6F_63_63_75_72_72_65_6E_63_65 : LocalName = LocalName :: pack_static (789u32) ;
pub const ATOM_LOCALNAME__74_69_6D_65 : LocalName = LocalName :: pack_static (790u32) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74_59 : LocalName = LocalName :: pack_static (791u32) ;
pub const ATOM_LOCALNAME__77_69_64_74_68 : LocalName = LocalName :: pack_static (792u32) ;
pub const ATOM_LOCALNAME__66_69_65_6C_64_73_65_74 : LocalName = LocalName :: pack_static (793u32) ;
pub const ATOM_LOCALNAME__73_74_61_72_74_4F_66_66_73_65_74 : LocalName = LocalName :: pack_static (794u32) ;
pub const ATOM_LOCALNAME__6F_74_68_65_72_77_69_73_65 : LocalName = LocalName :: pack_static (795u32) ;
pub const ATOM_LOCALNAME__63_68_61_72_6F_66_66 : LocalName = LocalName :: pack_static (796u32) ;
pub const ATOM_LOCALNAME__6C_61_6E_67_75_61_67_65 : LocalName = LocalName :: pack_static (797u32) ;
pub const ATOM_LOCALNAME__6C_69_6D_69_74 : LocalName = LocalName :: pack_static (798u32) ;
pub const ATOM_LOCALNAME__70_6C_61_69_6E_74_65_78_74 : LocalName = LocalName :: pack_static (799u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_6F_76_65_72 : LocalName = LocalName :: pack_static (800u32) ;
pub const ATOM_LOCALNAME__73_68_61_70_65_2D_72_65_6E_64_65_72_69_6E_67 : LocalName = LocalName :: pack_static (801u32) ;
pub const ATOM_LOCALNAME__62_6C_6F_63_6B_71_75_6F_74_65 : LocalName = LocalName :: pack_static (802u32) ;
pub const ATOM_LOCALNAME__66_65_62_6C_65_6E_64 : LocalName = LocalName :: pack_static (803u32) ;
pub const ATOM_LOCALNAME__6F_6E_6B_65_79_64_6F_77_6E : LocalName = LocalName :: pack_static (804u32) ;
pub const ATOM_LOCALNAME__69_6D_61_67_65 : LocalName = LocalName :: pack_static (805u32) ;
pub const ATOM_LOCALNAME__66_6E : LocalName = LocalName :: pack_static (806u32) ;
pub const ATOM_LOCALNAME__6E_6F_6D_6F_64_75_6C_65 : LocalName = LocalName :: pack_static (807u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_6E_73_3A_78_6C_69_6E_6B : LocalName = LocalName :: pack_static (808u32) ;
pub const ATOM_LOCALNAME__6E_6F_66_72_61_6D_65_73 : LocalName = LocalName :: pack_static (809u32) ;
pub const ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68_44_65_66 : LocalName = LocalName :: pack_static (810u32) ;
pub const ATOM_LOCALNAME__69_72_72_65_6C_65_76_61_6E_74 : LocalName = LocalName :: pack_static (811u32) ;
pub const ATOM_LOCALNAME__6E_6F_6E_63_65 : LocalName = LocalName :: pack_static (812u32) ;
pub const ATOM_LOCALNAME__62_61_63_6B_67_72_6F_75_6E_64 : LocalName = LocalName :: pack_static (813u32) ;
pub const ATOM_LOCALNAME__66_6F_72_61_6C_6C : LocalName = LocalName :: pack_static (814u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_6E_73 : LocalName = LocalName :: pack_static (815u32) ;
pub const ATOM_LOCALNAME__72_65_73_75_6C_74 : LocalName = LocalName :: pack_static (816u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_64_61_73_68_61_72_72_61_79 : LocalName = LocalName :: pack_static (817u32) ;
pub const ATOM_LOCALNAME__61_62_73 : LocalName = LocalName :: pack_static (818u32) ;
pub const ATOM_LOCALNAME__64_65_74_61_69_6C_73 : LocalName = LocalName :: pack_static (819u32) ;
pub const ATOM_LOCALNAME__74_72_61_6E_73_6C_61_74_65 : LocalName = LocalName :: pack_static (820u32) ;
pub const ATOM_LOCALNAME__65_71_75_69_76_61_6C_65_6E_74 : LocalName = LocalName :: pack_static (821u32) ;
pub const ATOM_LOCALNAME__66_61_63_74_6F_72_69_61_6C : LocalName = LocalName :: pack_static (822u32) ;
pub const ATOM_LOCALNAME__70_72_6F_6D_70_74 : LocalName = LocalName :: pack_static (823u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_76_61_72_69_61_6E_74 : LocalName = LocalName :: pack_static (824u32) ;
pub const ATOM_LOCALNAME__74_69_6D_65_73 : LocalName = LocalName :: pack_static (825u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64_45_78_74_65_6E_73_69_6F_6E_73 : LocalName = LocalName :: pack_static (826u32) ;
pub const ATOM_LOCALNAME__73_74_69_74_63_68_54_69_6C_65_73 : LocalName = LocalName :: pack_static (827u32) ;
pub const ATOM_LOCALNAME__75_70_6C_69_6D_69_74 : LocalName = LocalName :: pack_static (828u32) ;
pub const ATOM_LOCALNAME__66_65_66_75_6E_63_67 : LocalName = LocalName :: pack_static (829u32) ;
pub const ATOM_LOCALNAME__73_70_72_65_61_64_6D_65_74_68_6F_64 : LocalName = LocalName :: pack_static (830u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_65_70_65_61_74 : LocalName = LocalName :: pack_static (831u32) ;
pub const ATOM_LOCALNAME__6F_6E_6F_66_66_6C_69_6E_65 : LocalName = LocalName :: pack_static (832u32) ;
pub const ATOM_LOCALNAME__61_75_74_6F_73_75_62_6D_69_74 : LocalName = LocalName :: pack_static (833u32) ;
pub const ATOM_LOCALNAME__6C_6F_67 : LocalName = LocalName :: pack_static (834u32) ;
pub const ATOM_LOCALNAME__73_70_61_63_65_72 : LocalName = LocalName :: pack_static (835u32) ;
pub const ATOM_LOCALNAME__63_61_70_2D_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (836u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_65_6C_75_6E_69_74_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (837u32) ;
pub const ATOM_LOCALNAME__6F_6E_6C_61_6E_67_75_61_67_65_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (838u32) ;
pub const ATOM_LOCALNAME__63_6F_6F_72_64_73 : LocalName = LocalName :: pack_static (839u32) ;
pub const ATOM_LOCALNAME__69_6E_66_69_6E_69_74_79 : LocalName = LocalName :: pack_static (840u32) ;
pub const ATOM_LOCALNAME__62_67_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (841u32) ;
pub const ATOM_LOCALNAME__63_6F_64_65_74_79_70_65 : LocalName = LocalName :: pack_static (842u32) ;
pub const ATOM_LOCALNAME__6B_65_79_74_69_6D_65_73 : LocalName = LocalName :: pack_static (843u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_70_72_69_6E_74 : LocalName = LocalName :: pack_static (844u32) ;
pub const ATOM_LOCALNAME__6F_76_65_72_6C_69_6E_65_2D_70_6F_73_69_74_69_6F_6E : LocalName = LocalName :: pack_static (845u32) ;
pub const ATOM_LOCALNAME__69_6D_70_6C_69_65_73 : LocalName = LocalName :: pack_static (846u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_6F_77_73_69_6E_73_65_72_74_65_64 : LocalName = LocalName :: pack_static (847u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_73_63_72_69_70_74_74_79_70_65 : LocalName = LocalName :: pack_static (848u32) ;
pub const ATOM_LOCALNAME__68_65_61_64 : LocalName = LocalName :: pack_static (849u32) ;
pub const ATOM_LOCALNAME__76_61_6C_69_67_6E : LocalName = LocalName :: pack_static (850u32) ;
pub const ATOM_LOCALNAME__6E_61_6D_65 : LocalName = LocalName :: pack_static (851u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_54_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (852u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_78 : LocalName = LocalName :: pack_static (853u32) ;
pub const ATOM_LOCALNAME__6C_61_70_6C_61_63_69_61_6E : LocalName = LocalName :: pack_static (854u32) ;
pub const ATOM_LOCALNAME__6D_65_74_61_64_61_74_61 : LocalName = LocalName :: pack_static (855u32) ;
pub const ATOM_LOCALNAME__61_63_74_69_6F_6E_74_79_70_65 : LocalName = LocalName :: pack_static (856u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_5A : LocalName = LocalName :: pack_static (857u32) ;
pub const ATOM_LOCALNAME__63_6C_61_73_73 : LocalName = LocalName :: pack_static (858u32) ;
pub const ATOM_LOCALNAME__77_69_64_74_68_73 : LocalName = LocalName :: pack_static (859u32) ;
pub const ATOM_LOCALNAME__64_6F_77_6E_6C_6F_61_64 : LocalName = LocalName :: pack_static (860u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6F_75_74 : LocalName = LocalName :: pack_static (861u32) ;
pub const ATOM_LOCALNAME__75_31 : LocalName = LocalName :: pack_static (862u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_70_72_6F_66_69_6C_65 : LocalName = LocalName :: pack_static (863u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_61_74_61_61_76_61_69_6C_61_62_6C_65 : LocalName = LocalName :: pack_static (864u32) ;
pub const ATOM_LOCALNAME__79 : LocalName = LocalName :: pack_static (865u32) ;
pub const ATOM_LOCALNAME__75_6E_73_65_6C_65_63_74_61_62_6C_65 : LocalName = LocalName :: pack_static (866u32) ;
pub const ATOM_LOCALNAME__6F_72 : LocalName = LocalName :: pack_static (867u32) ;
pub const ATOM_LOCALNAME__6D_73_70_61_63_65 : LocalName = LocalName :: pack_static (868u32) ;
pub const ATOM_LOCALNAME__61_6C_70_68_61_62_65_74_69_63 : LocalName = LocalName :: pack_static (869u32) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74_79 : LocalName = LocalName :: pack_static (870u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72_75_6E_69_74_73 : LocalName = LocalName :: pack_static (871u32) ;
pub const ATOM_LOCALNAME__66_65_4D_65_72_67_65_4E_6F_64_65 : LocalName = LocalName :: pack_static (872u32) ;
pub const ATOM_LOCALNAME__73_79_73_74_65_6D_6C_61_6E_67_75_61_67_65 : LocalName = LocalName :: pack_static (873u32) ;
pub const ATOM_LOCALNAME__6F_6E_6B_65_79_70_72_65_73_73 : LocalName = LocalName :: pack_static (874u32) ;
pub const ATOM_LOCALNAME__64 : LocalName = LocalName :: pack_static (875u32) ;
pub const ATOM_LOCALNAME__66_72_61_6D_65_62_6F_72_64_65_72 : LocalName = LocalName :: pack_static (876u32) ;
pub const ATOM_LOCALNAME__66_65_50_6F_69_6E_74_4C_69_67_68_74 : LocalName = LocalName :: pack_static (877u32) ;
pub const ATOM_LOCALNAME__73_75_6D : LocalName = LocalName :: pack_static (878u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_63_6F_70_79 : LocalName = LocalName :: pack_static (879u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_65_73_69_7A_65 : LocalName = LocalName :: pack_static (880u32) ;
pub const ATOM_LOCALNAME__79_43_68_61_6E_6E_65_6C_53_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (881u32) ;
pub const ATOM_LOCALNAME__73_69_6E_68 : LocalName = LocalName :: pack_static (882u32) ;
pub const ATOM_LOCALNAME__69 : LocalName = LocalName :: pack_static (883u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_61_74_61_73_65_74_63_68_61_6E_67_65_64 : LocalName = LocalName :: pack_static (884u32) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74_58 : LocalName = LocalName :: pack_static (885u32) ;
pub const ATOM_LOCALNAME__76_61_72_69_61_6E_63_65 : LocalName = LocalName :: pack_static (886u32) ;
pub const ATOM_LOCALNAME__73_74_65_6D_76 : LocalName = LocalName :: pack_static (887u32) ;
pub const ATOM_LOCALNAME__62_69_67 : LocalName = LocalName :: pack_static (888u32) ;
pub const ATOM_LOCALNAME__73_69_6E : LocalName = LocalName :: pack_static (889u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72_72_65_73 : LocalName = LocalName :: pack_static (890u32) ;
pub const ATOM_LOCALNAME__6D_6F_7A_62_72_6F_77_73_65_72 : LocalName = LocalName :: pack_static (891u32) ;
pub const ATOM_LOCALNAME__69_64_65_6F_67_72_61_70_68_69_63 : LocalName = LocalName :: pack_static (892u32) ;
pub const ATOM_LOCALNAME__6F_6E_68_65_6C_70 : LocalName = LocalName :: pack_static (893u32) ;
pub const ATOM_LOCALNAME__61_63_74_69_6F_6E : LocalName = LocalName :: pack_static (894u32) ;
pub const ATOM_LOCALNAME__6C_6F_6E_67_64_65_73_63 : LocalName = LocalName :: pack_static (895u32) ;
pub const ATOM_LOCALNAME__66_78 : LocalName = LocalName :: pack_static (896u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_61_72_65_61 : LocalName = LocalName :: pack_static (897u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_72_73_65_63_74 : LocalName = LocalName :: pack_static (898u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_69_66_69_63_61_74_69_6F_6E : LocalName = LocalName :: pack_static (899u32) ;
pub const ATOM_LOCALNAME__76_65_72_79_76_65_72_79_74_68_69_6E_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (900u32) ;
pub const ATOM_LOCALNAME__73_74_72_69_6B_65_74_68_72_6F_75_67_68_2D_74_68_69_63_6B_6E_65_73_73 : LocalName = LocalName :: pack_static (901u32) ;
pub const ATOM_LOCALNAME__73_61_6E_64_62_6F_78 : LocalName = LocalName :: pack_static (902u32) ;
pub const ATOM_LOCALNAME__6F_6E_69_6E_76_61_6C_69_64 : LocalName = LocalName :: pack_static (903u32) ;
pub const ATOM_LOCALNAME__66_6F_72_65_69_67_6E_4F_62_6A_65_63_74 : LocalName = LocalName :: pack_static (904u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_62_75_73_79 : LocalName = LocalName :: pack_static (905u32) ;
pub const ATOM_LOCALNAME__66_65_74_69_6C_65 : LocalName = LocalName :: pack_static (906u32) ;
pub const ATOM_LOCALNAME__66_6C_6F_6F_64_2D_6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (907u32) ;
pub const ATOM_LOCALNAME__6D_61_70 : LocalName = LocalName :: pack_static (908u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_2D_72_65_6E_64_65_72_69_6E_67 : LocalName = LocalName :: pack_static (909u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_76_65_73_74_61_72_74 : LocalName = LocalName :: pack_static (910u32) ;
pub const ATOM_LOCALNAME__63_69_72_63_6C_65 : LocalName = LocalName :: pack_static (911u32) ;
pub const ATOM_LOCALNAME__62_75_74_74_6F_6E : LocalName = LocalName :: pack_static (912u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6D_69_74_65_72_6C_69_6D_69_74 : LocalName = LocalName :: pack_static (913u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_6C_69_6E_65_2D_73_68_69_66_74 : LocalName = LocalName :: pack_static (914u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_61_6E_75_6D_62_65_72 : LocalName = LocalName :: pack_static (915u32) ;
pub const ATOM_LOCALNAME__62 : LocalName = LocalName :: pack_static (916u32) ;
pub const ATOM_LOCALNAME__66_65_43_6F_6E_76_6F_6C_76_65_4D_61_74_72_69_78 : LocalName = LocalName :: pack_static (917u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_69_6E_67 : LocalName = LocalName :: pack_static (918u32) ;
pub const ATOM_LOCALNAME__6B_65_79_50_6F_69_6E_74_73 : LocalName = LocalName :: pack_static (919u32) ;
pub const ATOM_LOCALNAME__6B_33 : LocalName = LocalName :: pack_static (920u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_78_74_6D_65_6E_75 : LocalName = LocalName :: pack_static (921u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_73_65_74_73_69_7A_65 : LocalName = LocalName :: pack_static (922u32) ;
pub const ATOM_LOCALNAME__74_72_61_6E_73_70_6F_73_65 : LocalName = LocalName :: pack_static (923u32) ;
pub const ATOM_LOCALNAME__6C_69_73_74_65_6E_65_72 : LocalName = LocalName :: pack_static (924u32) ;
pub const ATOM_LOCALNAME__64_69_72 : LocalName = LocalName :: pack_static (925u32) ;
pub const ATOM_LOCALNAME__65_64_67_65_6D_6F_64_65 : LocalName = LocalName :: pack_static (926u32) ;
pub const ATOM_LOCALNAME__66_61_63_65 : LocalName = LocalName :: pack_static (927u32) ;
pub const ATOM_LOCALNAME__62_65_67_69_6E : LocalName = LocalName :: pack_static (928u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_72_6F_6C_6C_65_72_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (929u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6F_76_65_72 : LocalName = LocalName :: pack_static (930u32) ;
pub const ATOM_LOCALNAME__6D_65_74_61 : LocalName = LocalName :: pack_static (931u32) ;
pub const ATOM_LOCALNAME__6D_61_6C_69_67_6E_6D_61_72_6B : LocalName = LocalName :: pack_static (932u32) ;
pub const ATOM_LOCALNAME__6B_31 : LocalName = LocalName :: pack_static (933u32) ;
pub const ATOM_LOCALNAME__6D_65_74_65_72 : LocalName = LocalName :: pack_static (934u32) ;
pub const ATOM_LOCALNAME__70_6C_75_73 : LocalName = LocalName :: pack_static (935u32) ;
pub const ATOM_LOCALNAME__61_72_63_63_6F_74 : LocalName = LocalName :: pack_static (936u32) ;
pub const ATOM_LOCALNAME__6D_66_65_6E_63_65_64 : LocalName = LocalName :: pack_static (937u32) ;
pub const ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68 : LocalName = LocalName :: pack_static (938u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_74_65_6D_70_6C_61_74_65_69_64 : LocalName = LocalName :: pack_static (939u32) ;
pub const ATOM_LOCALNAME__78_43_68_61_6E_6E_65_6C_53_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (940u32) ;
pub const ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_75_72_6C : LocalName = LocalName :: pack_static (941u32) ;
pub const ATOM_LOCALNAME__74_65_78_74 : LocalName = LocalName :: pack_static (942u32) ;
pub const ATOM_LOCALNAME__73_72_63_64_6F_63 : LocalName = LocalName :: pack_static (943u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_72_6D_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (944u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_46_72_65_71_75_65_6E_63_79 : LocalName = LocalName :: pack_static (945u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_74_69_74_6C_65 : LocalName = LocalName :: pack_static (946u32) ;
pub const ATOM_LOCALNAME__6D_61_78 : LocalName = LocalName :: pack_static (947u32) ;
pub const ATOM_LOCALNAME__72_74_63 : LocalName = LocalName :: pack_static (948u32) ;
pub const ATOM_LOCALNAME__74_65_6E_64_73_74_6F : LocalName = LocalName :: pack_static (949u32) ;
pub const ATOM_LOCALNAME__61_6C_69_67_6E_6D_65_6E_74_73_63_6F_70_65 : LocalName = LocalName :: pack_static (950u32) ;
pub const ATOM_LOCALNAME__69_6E_73 : LocalName = LocalName :: pack_static (951u32) ;
pub const ATOM_LOCALNAME__74_69_74_6C_65 : LocalName = LocalName :: pack_static (952u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_72_6F_70_65_72_74_79_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (953u32) ;
pub const ATOM_LOCALNAME__65_6E_63_6F_64_69_6E_67 : LocalName = LocalName :: pack_static (954u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_70_72_73_75_62_73_65_74 : LocalName = LocalName :: pack_static (955u32) ;
pub const ATOM_LOCALNAME__6F_70_65_72_61_74_6F_72 : LocalName = LocalName :: pack_static (956u32) ;
pub const ATOM_LOCALNAME__75_6E_64_65_72_6C_69_6E_65_2D_74_68_69_63_6B_6E_65_73_73 : LocalName = LocalName :: pack_static (957u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_70_61_73_74_65 : LocalName = LocalName :: pack_static (958u32) ;
pub const ATOM_LOCALNAME__66_65_63_6F_6D_70_6F_6E_65_6E_74_74_72_61_6E_73_66_65_72 : LocalName = LocalName :: pack_static (959u32) ;
pub const ATOM_LOCALNAME__68_65_69_67_68_74 : LocalName = LocalName :: pack_static (960u32) ;
pub const ATOM_LOCALNAME__73_6F_6C_69_64_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (961u32) ;
pub const ATOM_LOCALNAME__66_65_6D_6F_72_70_68_6F_6C_6F_67_79 : LocalName = LocalName :: pack_static (962u32) ;
pub const ATOM_LOCALNAME__76_65_72_74_2D_6F_72_69_67_69_6E_2D_79 : LocalName = LocalName :: pack_static (963u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_61_74_69_6F_6E : LocalName = LocalName :: pack_static (964u32) ;
pub const ATOM_LOCALNAME__74_65_6D_70_6C_61_74_65 : LocalName = LocalName :: pack_static (965u32) ;
pub const ATOM_LOCALNAME__70_72_65 : LocalName = LocalName :: pack_static (966u32) ;
pub const ATOM_LOCALNAME__68_33 : LocalName = LocalName :: pack_static (967u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_2D_73_74_61_72_74 : LocalName = LocalName :: pack_static (968u32) ;
pub const ATOM_LOCALNAME__73_65_65_64 : LocalName = LocalName :: pack_static (969u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_72_65_66 : LocalName = LocalName :: pack_static (970u32) ;
pub const ATOM_LOCALNAME__6F_6E_65_72_72_6F_72_75_70_64_61_74_65 : LocalName = LocalName :: pack_static (971u32) ;
pub const ATOM_LOCALNAME__66_65_73_70_65_63_75_6C_61_72_6C_69_67_68_74_69_6E_67 : LocalName = LocalName :: pack_static (972u32) ;
pub const ATOM_LOCALNAME__66_65_66_6C_6F_6F_64 : LocalName = LocalName :: pack_static (973u32) ;
pub const ATOM_LOCALNAME__2A : LocalName = LocalName :: pack_static (974u32) ;
pub const ATOM_LOCALNAME__63_61_6E_76_61_73 : LocalName = LocalName :: pack_static (975u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_65_6C_65_63_74 : LocalName = LocalName :: pack_static (976u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_58 : LocalName = LocalName :: pack_static (977u32) ;
pub const ATOM_LOCALNAME__64_69_72_65_63_74_69_6F_6E : LocalName = LocalName :: pack_static (978u32) ;
pub const ATOM_LOCALNAME__6D_61_74_72_69_78_72_6F_77 : LocalName = LocalName :: pack_static (979u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_65_6E_63_74_79_70_65 : LocalName = LocalName :: pack_static (980u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_75_6E_69_74_73 : LocalName = LocalName :: pack_static (981u32) ;
pub const ATOM_LOCALNAME__68_65_61_64_65_72 : LocalName = LocalName :: pack_static (982u32) ;
pub const ATOM_LOCALNAME__6F_6E_6B_65_79_75_70 : LocalName = LocalName :: pack_static (983u32) ;
pub const ATOM_LOCALNAME__6C_65_6E_67_74_68_61_64_6A_75_73_74 : LocalName = LocalName :: pack_static (984u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B : LocalName = LocalName :: pack_static (985u32) ;
pub const ATOM_LOCALNAME__63_6E : LocalName = LocalName :: pack_static (986u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6E_6F_77 : LocalName = LocalName :: pack_static (987u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_77_69_64_74_68 : LocalName = LocalName :: pack_static (988u32) ;
pub const ATOM_LOCALNAME__61_73_63_65_6E_74 : LocalName = LocalName :: pack_static (989u32) ;
pub const ATOM_LOCALNAME__73_68_6F_77 : LocalName = LocalName :: pack_static (990u32) ;
pub const ATOM_LOCALNAME__70_61_72_61_6D : LocalName = LocalName :: pack_static (991u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_79 : LocalName = LocalName :: pack_static (992u32) ;
pub const ATOM_LOCALNAME__6D_73_71_72_74 : LocalName = LocalName :: pack_static (993u32) ;
pub const ATOM_LOCALNAME__72_65_61_64_6F_6E_6C_79 : LocalName = LocalName :: pack_static (994u32) ;
pub const ATOM_LOCALNAME__66_65_49_6D_61_67_65 : LocalName = LocalName :: pack_static (995u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_75_6E_69_74_73 : LocalName = LocalName :: pack_static (996u32) ;
pub const ATOM_LOCALNAME__73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (997u32) ;
pub const ATOM_LOCALNAME__77_6F_72_64_2D_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (998u32) ;
pub const ATOM_LOCALNAME__6D_61_72_71_75_65_65 : LocalName = LocalName :: pack_static (999u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_67_72_6F_75_70 : LocalName = LocalName :: pack_static (1000u32) ;
pub const ATOM_LOCALNAME__6F_6E_61_63_74_69_76_61_74_65 : LocalName = LocalName :: pack_static (1001u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_77_69_64_74_68 : LocalName = LocalName :: pack_static (1002u32) ;
pub const ATOM_LOCALNAME__61_6E_6E_6F_74_61_74_69_6F_6E_2D_78_6D_6C : LocalName = LocalName :: pack_static (1003u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_68_69_64_64_65_6E : LocalName = LocalName :: pack_static (1004u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64_65_78_74_65_6E_73_69_6F_6E_73 : LocalName = LocalName :: pack_static (1005u32) ;
pub const ATOM_LOCALNAME__63_68_61_72_73_65_74 : LocalName = LocalName :: pack_static (1006u32) ;
pub const ATOM_LOCALNAME__6D_6F_76_61_62_6C_65_6C_69_6D_69_74_73 : LocalName = LocalName :: pack_static (1007u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6C_69_6E_65_63_61_70 : LocalName = LocalName :: pack_static (1008u32) ;
pub const ATOM_LOCALNAME__65_6E_61_62_6C_65_2D_62_61_63_6B_67_72_6F_75_6E_64 : LocalName = LocalName :: pack_static (1009u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_74_6F_70 : LocalName = LocalName :: pack_static (1010u32) ;
pub const ATOM_LOCALNAME__73_69_7A_65 : LocalName = LocalName :: pack_static (1011u32) ;
pub const ATOM_LOCALNAME__6D_6F_6D_65_6E_74_61_62_6F_75_74 : LocalName = LocalName :: pack_static (1012u32) ;
pub const ATOM_LOCALNAME__67 : LocalName = LocalName :: pack_static (1013u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_66_6F_6E_74 : LocalName = LocalName :: pack_static (1014u32) ;
pub const ATOM_LOCALNAME__75 : LocalName = LocalName :: pack_static (1015u32) ;
pub const ATOM_LOCALNAME__6E_6F_74 : LocalName = LocalName :: pack_static (1016u32) ;
pub const ATOM_LOCALNAME__68_34 : LocalName = LocalName :: pack_static (1017u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_74_68_69_63_6B_6E_65_73_73 : LocalName = LocalName :: pack_static (1018u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_72_65_6C_65_76_61_6E_74 : LocalName = LocalName :: pack_static (1019u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_62_72_65_61_6B : LocalName = LocalName :: pack_static (1020u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6C_65_61_76_65 : LocalName = LocalName :: pack_static (1021u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_3A_6C_61_6E_67 : LocalName = LocalName :: pack_static (1022u32) ;
pub const ATOM_LOCALNAME__6F_76_65_72_6C_69_6E_65_2D_74_68_69_63_6B_6E_65_73_73 : LocalName = LocalName :: pack_static (1023u32) ;
pub const ATOM_LOCALNAME__78_6F_72 : LocalName = LocalName :: pack_static (1024u32) ;
pub const ATOM_LOCALNAME__72_65_63_74 : LocalName = LocalName :: pack_static (1025u32) ;
pub const ATOM_LOCALNAME__6D_6D_75_6C_74_69_73_63_72_69_70_74_73 : LocalName = LocalName :: pack_static (1026u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_6F_77_73_64_65_6C_65_74_65 : LocalName = LocalName :: pack_static (1027u32) ;
pub const ATOM_LOCALNAME__62_64_6F : LocalName = LocalName :: pack_static (1028u32) ;
pub const ATOM_LOCALNAME__61_63_63_75_6D_75_6C_61_74_65 : LocalName = LocalName :: pack_static (1029u32) ;
pub const ATOM_LOCALNAME__73_65_63_74_69_6F_6E : LocalName = LocalName :: pack_static (1030u32) ;
pub const ATOM_LOCALNAME__64_69_76_69_64_65 : LocalName = LocalName :: pack_static (1031u32) ;
pub const ATOM_LOCALNAME__69_6D_67 : LocalName = LocalName :: pack_static (1032u32) ;
pub const ATOM_LOCALNAME__6C_61_6D_62_64_61 : LocalName = LocalName :: pack_static (1033u32) ;
pub const ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_75_6E_69_74_73 : LocalName = LocalName :: pack_static (1034u32) ;
pub const ATOM_LOCALNAME__70_72_65_73_65_72_76_65_61_6C_70_68_61 : LocalName = LocalName :: pack_static (1035u32) ;
pub const ATOM_LOCALNAME__73_6D_61_6C_6C : LocalName = LocalName :: pack_static (1036u32) ;
pub const ATOM_LOCALNAME__66_72_61_6D_65_73_65_74 : LocalName = LocalName :: pack_static (1037u32) ;
pub const ATOM_LOCALNAME__76_61_72 : LocalName = LocalName :: pack_static (1038u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74 : LocalName = LocalName :: pack_static (1039u32) ;
pub const ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_4E_61_6D_65 : LocalName = LocalName :: pack_static (1040u32) ;
pub const ATOM_LOCALNAME__6F_6E_65_6E_64 : LocalName = LocalName :: pack_static (1041u32) ;
pub const ATOM_LOCALNAME__74_68_69_6E_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (1042u32) ;
pub const ATOM_LOCALNAME__76_69_65_77_62_6F_78 : LocalName = LocalName :: pack_static (1043u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_73_74_61_72_74 : LocalName = LocalName :: pack_static (1044u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_52_65_66 : LocalName = LocalName :: pack_static (1045u32) ;
pub const ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_6E_61_6D_65 : LocalName = LocalName :: pack_static (1046u32) ;
pub const ATOM_LOCALNAME__61_6C_74 : LocalName = LocalName :: pack_static (1047u32) ;
pub const ATOM_LOCALNAME__63_69_74_65 : LocalName = LocalName :: pack_static (1048u32) ;
pub const ATOM_LOCALNAME__72_6F_77_61_6C_69_67_6E : LocalName = LocalName :: pack_static (1049u32) ;
pub const ATOM_LOCALNAME__65_78_74_65_72_6E_61_6C_52_65_73_6F_75_72_63_65_73_52_65_71_75_69_72_65_64 : LocalName = LocalName :: pack_static (1050u32) ;
pub const ATOM_LOCALNAME__66_65_44_69_66_66_75_73_65_4C_69_67_68_74_69_6E_67 : LocalName = LocalName :: pack_static (1051u32) ;
pub const ATOM_LOCALNAME__76_69_64_65_6F : LocalName = LocalName :: pack_static (1052u32) ;
pub const ATOM_LOCALNAME__72_6F_77_6C_69_6E_65_73 : LocalName = LocalName :: pack_static (1053u32) ;
pub const ATOM_LOCALNAME__73_74_64_44_65_76_69_61_74_69_6F_6E : LocalName = LocalName :: pack_static (1054u32) ;
pub const ATOM_LOCALNAME__63_6F_6D_70_61_63_74 : LocalName = LocalName :: pack_static (1055u32) ;
pub const ATOM_LOCALNAME__68_69_64_65_66_6F_63_75_73 : LocalName = LocalName :: pack_static (1056u32) ;
pub const ATOM_LOCALNAME__68_61_6E_67_69_6E_67 : LocalName = LocalName :: pack_static (1057u32) ;
pub const ATOM_LOCALNAME__73_65_70_61_72_61_74_6F_72_73 : LocalName = LocalName :: pack_static (1058u32) ;
pub const ATOM_LOCALNAME__73_63_72_69_70_74_6D_69_6E_73_69_7A_65 : LocalName = LocalName :: pack_static (1059u32) ;
pub const ATOM_LOCALNAME__62_61_73_65 : LocalName = LocalName :: pack_static (1060u32) ;
pub const ATOM_LOCALNAME__63_6F_6C : LocalName = LocalName :: pack_static (1061u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73 : LocalName = LocalName :: pack_static (1062u32) ;
pub const ATOM_LOCALNAME__74_62_6F_64_79 : LocalName = LocalName :: pack_static (1063u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_6C_69_63_6B : LocalName = LocalName :: pack_static (1064u32) ;
pub const ATOM_LOCALNAME__66_65_64_69_66_66_75_73_65_6C_69_67_68_74_69_6E_67 : LocalName = LocalName :: pack_static (1065u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_61_74_6F_6D_69_63 : LocalName = LocalName :: pack_static (1066u32) ;
pub const ATOM_LOCALNAME__73_74_72_69_6B_65_74_68_72_6F_75_67_68_2D_70_6F_73_69_74_69_6F_6E : LocalName = LocalName :: pack_static (1067u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (1068u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6C_69_76_65 : LocalName = LocalName :: pack_static (1069u32) ;
pub const ATOM_LOCALNAME__61_7A_69_6D_75_74_68 : LocalName = LocalName :: pack_static (1070u32) ;
pub const ATOM_LOCALNAME__72_61_64_69_61_6C_67_72_61_64_69_65_6E_74 : LocalName = LocalName :: pack_static (1071u32) ;
pub const ATOM_LOCALNAME__66_65_54_69_6C_65 : LocalName = LocalName :: pack_static (1072u32) ;
pub const ATOM_LOCALNAME__6B_65_79_53_70_6C_69_6E_65_73 : LocalName = LocalName :: pack_static (1073u32) ;
pub const ATOM_LOCALNAME__63_6C_61_73_73_69_64 : LocalName = LocalName :: pack_static (1074u32) ;
pub const ATOM_LOCALNAME__72_65_73_74_61_72_74 : LocalName = LocalName :: pack_static (1075u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_65_61_63_74_69_76_61_74_65 : LocalName = LocalName :: pack_static (1076u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_50_61_74_68_55_6E_69_74_73 : LocalName = LocalName :: pack_static (1077u32) ;
pub const ATOM_LOCALNAME__72_65_6C_6E : LocalName = LocalName :: pack_static (1078u32) ;
pub const ATOM_LOCALNAME__61_72_63_73_69_6E : LocalName = LocalName :: pack_static (1079u32) ;
pub const ATOM_LOCALNAME__64_69_73_61_62_6C_65_64 : LocalName = LocalName :: pack_static (1080u32) ;
pub const ATOM_LOCALNAME__63_61_70_74_69_6F_6E : LocalName = LocalName :: pack_static (1081u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_74_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (1082u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_72_65_61_64_6F_6E_6C_79 : LocalName = LocalName :: pack_static (1083u32) ;
pub const ATOM_LOCALNAME__73_68_61_70_65 : LocalName = LocalName :: pack_static (1084u32) ;
pub const ATOM_LOCALNAME__6E_6F_73_68_61_64_65 : LocalName = LocalName :: pack_static (1085u32) ;
pub const ATOM_LOCALNAME__76_2D_6D_61_74_68_65_6D_61_74_69_63_61_6C : LocalName = LocalName :: pack_static (1086u32) ;
pub const ATOM_LOCALNAME__6D_70_61_64_64_65_64 : LocalName = LocalName :: pack_static (1087u32) ;
pub const ATOM_LOCALNAME__64_65_70_74_68 : LocalName = LocalName :: pack_static (1088u32) ;
pub const ATOM_LOCALNAME__66_65_4F_66_66_73_65_74 : LocalName = LocalName :: pack_static (1089u32) ;
pub const ATOM_LOCALNAME__6B_65_79_70_6F_69_6E_74_73 : LocalName = LocalName :: pack_static (1090u32) ;
pub const ATOM_LOCALNAME__79_31 : LocalName = LocalName :: pack_static (1091u32) ;
pub const ATOM_LOCALNAME__66_65_66_75_6E_63_72 : LocalName = LocalName :: pack_static (1092u32) ;
pub const ATOM_LOCALNAME__64_69_76_69_73_6F_72 : LocalName = LocalName :: pack_static (1093u32) ;
pub const ATOM_LOCALNAME__6D_61_72_67_69_6E_77_69_64_74_68 : LocalName = LocalName :: pack_static (1094u32) ;
pub const ATOM_LOCALNAME__68_67_72_6F_75_70 : LocalName = LocalName :: pack_static (1095u32) ;
pub const ATOM_LOCALNAME__61_72_63_63_6F_73_68 : LocalName = LocalName :: pack_static (1096u32) ;
pub const ATOM_LOCALNAME__63_6F_74 : LocalName = LocalName :: pack_static (1097u32) ;
pub const ATOM_LOCALNAME__64_61_74_61_6C_69_73_74 : LocalName = LocalName :: pack_static (1098u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68 : LocalName = LocalName :: pack_static (1099u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_65_6C_4D_61_74_72_69_78 : LocalName = LocalName :: pack_static (1100u32) ;
pub const ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_54_79_70_65 : LocalName = LocalName :: pack_static (1101u32) ;
pub const ATOM_LOCALNAME__73_65_74_64_69_66_66 : LocalName = LocalName :: pack_static (1102u32) ;
pub const ATOM_LOCALNAME__75_32 : LocalName = LocalName :: pack_static (1103u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_6C_65_61_76_65 : LocalName = LocalName :: pack_static (1104u32) ;
pub const ATOM_LOCALNAME__73_63_72_6F_6C_6C_69_6E_67 : LocalName = LocalName :: pack_static (1105u32) ;
pub const ATOM_LOCALNAME__7A_6F_6F_6D_41_6E_64_50_61_6E : LocalName = LocalName :: pack_static (1106u32) ;
pub const ATOM_LOCALNAME__62_79 : LocalName = LocalName :: pack_static (1107u32) ;
pub const ATOM_LOCALNAME__64_69_66_66 : LocalName = LocalName :: pack_static (1108u32) ;
# [doc = "Takes a local name as a string and returns its key in the string cache."] # [macro_export] macro_rules ! local_name { ("dur") => { $ crate :: ATOM_LOCALNAME__64_75_72 } ;
("units-per-em") => { $ crate :: ATOM_LOCALNAME__75_6E_69_74_73_2D_70_65_72_2D_65_6D } ;
("selected") => { $ crate :: ATOM_LOCALNAME__73_65_6C_65_63_74_65_64 } ;
("patternTransform") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_54_72_61_6E_73_66_6F_72_6D } ;
("th") => { $ crate :: ATOM_LOCALNAME__74_68 } ;
("and") => { $ crate :: ATOM_LOCALNAME__61_6E_64 } ;
("aria-secret") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_73_65_63_72_65_74 } ;
("mglyph") => { $ crate :: ATOM_LOCALNAME__6D_67_6C_79_70_68 } ;
("grad") => { $ crate :: ATOM_LOCALNAME__67_72_61_64 } ;
("select") => { $ crate :: ATOM_LOCALNAME__73_65_6C_65_63_74 } ;
("lowsrc") => { $ crate :: ATOM_LOCALNAME__6C_6F_77_73_72_63 } ;
("line-height") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_2D_68_65_69_67_68_74 } ;
("geq") => { $ crate :: ATOM_LOCALNAME__67_65_71 } ;
("ping") => { $ crate :: ATOM_LOCALNAME__70_69_6E_67 } ;
("contentStyleType") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_53_74_79_6C_65_54_79_70_65 } ;
("aria-checked") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_63_68_65_63_6B_65_64 } ;
("ln") => { $ crate :: ATOM_LOCALNAME__6C_6E } ;
("dataformatas") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_66_6F_72_6D_61_74_61_73 } ;
("ondrag") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67 } ;
("declare") => { $ crate :: ATOM_LOCALNAME__64_65_63_6C_61_72_65 } ;
("onerror") => { $ crate :: ATOM_LOCALNAME__6F_6E_65_72_72_6F_72 } ;
("p") => { $ crate :: ATOM_LOCALNAME__70 } ;
("msup") => { $ crate :: ATOM_LOCALNAME__6D_73_75_70 } ;
("kbd") => { $ crate :: ATOM_LOCALNAME__6B_62_64 } ;
("marginheight") => { $ crate :: ATOM_LOCALNAME__6D_61_72_67_69_6E_68_65_69_67_68_74 } ;
("svg") => { $ crate :: ATOM_LOCALNAME__73_76_67 } ;
("filterRes") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72_52_65_73 } ;
("wrap") => { $ crate :: ATOM_LOCALNAME__77_72_61_70 } ;
("h1") => { $ crate :: ATOM_LOCALNAME__68_31 } ;
("noembed") => { $ crate :: ATOM_LOCALNAME__6E_6F_65_6D_62_65_64 } ;
("displaystyle") => { $ crate :: ATOM_LOCALNAME__64_69_73_70_6C_61_79_73_74_79_6C_65 } ;
("numoctaves") => { $ crate :: ATOM_LOCALNAME__6E_75_6D_6F_63_74_61_76_65_73 } ;
("address") => { $ crate :: ATOM_LOCALNAME__61_64_64_72_65_73_73 } ;
("async") => { $ crate :: ATOM_LOCALNAME__61_73_79_6E_63 } ;
("ychannelselector") => { $ crate :: ATOM_LOCALNAME__79_63_68_61_6E_6E_65_6C_73_65_6C_65_63_74_6F_72 } ;
("horiz-origin-x") => { $ crate :: ATOM_LOCALNAME__68_6F_72_69_7A_2D_6F_72_69_67_69_6E_2D_78 } ;
("aside") => { $ crate :: ATOM_LOCALNAME__61_73_69_64_65 } ;
("fecomposite") => { $ crate :: ATOM_LOCALNAME__66_65_63_6F_6D_70_6F_73_69_74_65 } ;
("onblur") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_6C_75_72 } ;
("diffuseconstant") => { $ crate :: ATOM_LOCALNAME__64_69_66_66_75_73_65_63_6F_6E_73_74_61_6E_74 } ;
("leq") => { $ crate :: ATOM_LOCALNAME__6C_65_71 } ;
("list") => { $ crate :: ATOM_LOCALNAME__6C_69_73_74 } ;
("axis") => { $ crate :: ATOM_LOCALNAME__61_78_69_73 } ;
("codomain") => { $ crate :: ATOM_LOCALNAME__63_6F_64_6F_6D_61_69_6E } ;
("product") => { $ crate :: ATOM_LOCALNAME__70_72_6F_64_75_63_74 } ;
("verythickmathspace") => { $ crate :: ATOM_LOCALNAME__76_65_72_79_74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 } ;
("zoomandpan") => { $ crate :: ATOM_LOCALNAME__7A_6F_6F_6D_61_6E_64_70_61_6E } ;
("fespotlight") => { $ crate :: ATOM_LOCALNAME__66_65_73_70_6F_74_6C_69_67_68_74 } ;
("values") => { $ crate :: ATOM_LOCALNAME__76_61_6C_75_65_73 } ;
("rspace") => { $ crate :: ATOM_LOCALNAME__72_73_70_61_63_65 } ;
("marker-mid") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_6D_69_64 } ;
("dirname") => { $ crate :: ATOM_LOCALNAME__64_69_72_6E_61_6D_65 } ;
("scrolldelay") => { $ crate :: ATOM_LOCALNAME__73_63_72_6F_6C_6C_64_65_6C_61_79 } ;
("onload") => { $ crate :: ATOM_LOCALNAME__6F_6E_6C_6F_61_64 } ;
("datasrc") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_73_72_63 } ;
("replace") => { $ crate :: ATOM_LOCALNAME__72_65_70_6C_61_63_65 } ;
("formmethod") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_6D_65_74_68_6F_64 } ;
("preserveAlpha") => { $ crate :: ATOM_LOCALNAME__70_72_65_73_65_72_76_65_41_6C_70_68_61 } ;
("flood-color") => { $ crate :: ATOM_LOCALNAME__66_6C_6F_6F_64_2D_63_6F_6C_6F_72 } ;
("minsize") => { $ crate :: ATOM_LOCALNAME__6D_69_6E_73_69_7A_65 } ;
("femergenode") => { $ crate :: ATOM_LOCALNAME__66_65_6D_65_72_67_65_6E_6F_64_65 } ;
("cy") => { $ crate :: ATOM_LOCALNAME__63_79 } ;
("feSpecularLighting") => { $ crate :: ATOM_LOCALNAME__66_65_53_70_65_63_75_6C_61_72_4C_69_67_68_74_69_6E_67 } ;
("formnovalidate") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_6E_6F_76_61_6C_69_64_61_74_65 } ;
("stop-opacity") => { $ crate :: ATOM_LOCALNAME__73_74_6F_70_2D_6F_70_61_63_69_74_79 } ;
("z") => { $ crate :: ATOM_LOCALNAME__7A } ;
("munder") => { $ crate :: ATOM_LOCALNAME__6D_75_6E_64_65_72 } ;
("slope") => { $ crate :: ATOM_LOCALNAME__73_6C_6F_70_65 } ;
("csymbol") => { $ crate :: ATOM_LOCALNAME__63_73_79_6D_62_6F_6C } ;
("index") => { $ crate :: ATOM_LOCALNAME__69_6E_64_65_78 } ;
("union") => { $ crate :: ATOM_LOCALNAME__75_6E_69_6F_6E } ;
("slot") => { $ crate :: ATOM_LOCALNAME__73_6C_6F_74 } ;
("missing-glyph") => { $ crate :: ATOM_LOCALNAME__6D_69_73_73_69_6E_67_2D_67_6C_79_70_68 } ;
("x-height") => { $ crate :: ATOM_LOCALNAME__78_2D_68_65_69_67_68_74 } ;
("ondblclick") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_62_6C_63_6C_69_63_6B } ;
("limitingConeAngle") => { $ crate :: ATOM_LOCALNAME__6C_69_6D_69_74_69_6E_67_43_6F_6E_65_41_6E_67_6C_65 } ;
("approx") => { $ crate :: ATOM_LOCALNAME__61_70_70_72_6F_78 } ;
("x") => { $ crate :: ATOM_LOCALNAME__78 } ;
("imaginaryi") => { $ crate :: ATOM_LOCALNAME__69_6D_61_67_69_6E_61_72_79_69 } ;
("pointer-events") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_65_72_2D_65_76_65_6E_74_73 } ;
("font-weight") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_77_65_69_67_68_74 } ;
("specularConstant") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_43_6F_6E_73_74_61_6E_74 } ;
("in2") => { $ crate :: ATOM_LOCALNAME__69_6E_32 } ;
("onfocusout") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_63_75_73_6F_75_74 } ;
("step") => { $ crate :: ATOM_LOCALNAME__73_74_65_70 } ;
("wbr") => { $ crate :: ATOM_LOCALNAME__77_62_72 } ;
("glyph-orientation-vertical") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_2D_6F_72_69_65_6E_74_61_74_69_6F_6E_2D_76_65_72_74_69_63_61_6C } ;
("feBlend") => { $ crate :: ATOM_LOCALNAME__66_65_42_6C_65_6E_64 } ;
("scriptsizemultiplier") => { $ crate :: ATOM_LOCALNAME__73_63_72_69_70_74_73_69_7A_65_6D_75_6C_74_69_70_6C_69_65_72 } ;
("hspace") => { $ crate :: ATOM_LOCALNAME__68_73_70_61_63_65 } ;
("columnspan") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_73_70_61_6E } ;
("progress") => { $ crate :: ATOM_LOCALNAME__70_72_6F_67_72_65_73_73 } ;
("autocomplete") => { $ crate :: ATOM_LOCALNAME__61_75_74_6F_63_6F_6D_70_6C_65_74_65 } ;
("rule") => { $ crate :: ATOM_LOCALNAME__72_75_6C_65 } ;
("ceiling") => { $ crate :: ATOM_LOCALNAME__63_65_69_6C_69_6E_67 } ;
("aria-owns") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6F_77_6E_73 } ;
("intercept") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_72_63_65_70_74 } ;
("space") => { $ crate :: ATOM_LOCALNAME__73_70_61_63_65 } ;
("nextid") => { $ crate :: ATOM_LOCALNAME__6E_65_78_74_69_64 } ;
("feMorphology") => { $ crate :: ATOM_LOCALNAME__66_65_4D_6F_72_70_68_6F_6C_6F_67_79 } ;
("cellspacing") => { $ crate :: ATOM_LOCALNAME__63_65_6C_6C_73_70_61_63_69_6E_67 } ;
("start") => { $ crate :: ATOM_LOCALNAME__73_74_61_72_74 } ;
("calcMode") => { $ crate :: ATOM_LOCALNAME__63_61_6C_63_4D_6F_64_65 } ;
("rem") => { $ crate :: ATOM_LOCALNAME__72_65_6D } ;
("legend") => { $ crate :: ATOM_LOCALNAME__6C_65_67_65_6E_64 } ;
("onstart") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_74_61_72_74 } ;
("path") => { $ crate :: ATOM_LOCALNAME__70_61_74_68 } ;
("feFuncG") => { $ crate :: ATOM_LOCALNAME__66_65_46_75_6E_63_47 } ;
("onafterprint") => { $ crate :: ATOM_LOCALNAME__6F_6E_61_66_74_65_72_70_72_69_6E_74 } ;
("primitiveUnits") => { $ crate :: ATOM_LOCALNAME__70_72_69_6D_69_74_69_76_65_55_6E_69_74_73 } ;
("altimg") => { $ crate :: ATOM_LOCALNAME__61_6C_74_69_6D_67 } ;
("refX") => { $ crate :: ATOM_LOCALNAME__72_65_66_58 } ;
("viewBox") => { $ crate :: ATOM_LOCALNAME__76_69_65_77_42_6F_78 } ;
("columnlines") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_6C_69_6E_65_73 } ;
("animateColor") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_43_6F_6C_6F_72 } ;
("patternUnits") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_55_6E_69_74_73 } ;
("optimum") => { $ crate :: ATOM_LOCALNAME__6F_70_74_69_6D_75_6D } ;
("onbounce") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_6F_75_6E_63_65 } ;
("lengthAdjust") => { $ crate :: ATOM_LOCALNAME__6C_65_6E_67_74_68_41_64_6A_75_73_74 } ;
("srclang") => { $ crate :: ATOM_LOCALNAME__73_72_63_6C_61_6E_67 } ;
("alttext") => { $ crate :: ATOM_LOCALNAME__61_6C_74_74_65_78_74 } ;
("mathbackground") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_62_61_63_6B_67_72_6F_75_6E_64 } ;
("aria-required") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_72_65_71_75_69_72_65_64 } ;
("ellipse") => { $ crate :: ATOM_LOCALNAME__65_6C_6C_69_70_73_65 } ;
("iframe") => { $ crate :: ATOM_LOCALNAME__69_66_72_61_6D_65 } ;
("altGlyphItem") => { $ crate :: ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68_49_74_65_6D } ;
("v-hanging") => { $ crate :: ATOM_LOCALNAME__76_2D_68_61_6E_67_69_6E_67 } ;
("lang") => { $ crate :: ATOM_LOCALNAME__6C_61_6E_67 } ;
("fecolormatrix") => { $ crate :: ATOM_LOCALNAME__66_65_63_6F_6C_6F_72_6D_61_74_72_69_78 } ;
("alignment-baseline") => { $ crate :: ATOM_LOCALNAME__61_6C_69_67_6E_6D_65_6E_74_2D_62_61_73_65_6C_69_6E_65 } ;
("mi") => { $ crate :: ATOM_LOCALNAME__6D_69 } ;
("tt") => { $ crate :: ATOM_LOCALNAME__74_74 } ;
("xlink") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B } ;
("macros") => { $ crate :: ATOM_LOCALNAME__6D_61_63_72_6F_73 } ;
("for") => { $ crate :: ATOM_LOCALNAME__66_6F_72 } ;
("onmousewheel") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_77_68_65_65_6C } ;
("script") => { $ crate :: ATOM_LOCALNAME__73_63_72_69_70_74 } ;
("tbreak") => { $ crate :: ATOM_LOCALNAME__74_62_72_65_61_6B } ;
("overflow") => { $ crate :: ATOM_LOCALNAME__6F_76_65_72_66_6C_6F_77 } ;
("underline-position") => { $ crate :: ATOM_LOCALNAME__75_6E_64_65_72_6C_69_6E_65_2D_70_6F_73_69_74_69_6F_6E } ;
("stddeviation") => { $ crate :: ATOM_LOCALNAME__73_74_64_64_65_76_69_61_74_69_6F_6E } ;
("prefetch") => { $ crate :: ATOM_LOCALNAME__70_72_65_66_65_74_63_68 } ;
("dl") => { $ crate :: ATOM_LOCALNAME__64_6C } ;
("char") => { $ crate :: ATOM_LOCALNAME__63_68_61_72 } ;
("text-anchor") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_2D_61_6E_63_68_6F_72 } ;
("nav") => { $ crate :: ATOM_LOCALNAME__6E_61_76 } ;
("scheme") => { $ crate :: ATOM_LOCALNAME__73_63_68_65_6D_65 } ;
("bgsound") => { $ crate :: ATOM_LOCALNAME__62_67_73_6F_75_6E_64 } ;
("unicode-range") => { $ crate :: ATOM_LOCALNAME__75_6E_69_63_6F_64_65_2D_72_61_6E_67_65 } ;
("markerHeight") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_48_65_69_67_68_74 } ;
("br") => { $ crate :: ATOM_LOCALNAME__62_72 } ;
("none") => { $ crate :: ATOM_LOCALNAME__6E_6F_6E_65 } ;
("attributetype") => { $ crate :: ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_74_79_70_65 } ;
("patterncontentunits") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_63_6F_6E_74_65_6E_74_75_6E_69_74_73 } ;
("tfoot") => { $ crate :: ATOM_LOCALNAME__74_66_6F_6F_74 } ;
("onrowenter") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_6F_77_65_6E_74_65_72 } ;
("crossorigin") => { $ crate :: ATOM_LOCALNAME__63_72_6F_73_73_6F_72_69_67_69_6E } ;
("textLength") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_4C_65_6E_67_74_68 } ;
("fedistantlight") => { $ crate :: ATOM_LOCALNAME__66_65_64_69_73_74_61_6E_74_6C_69_67_68_74 } ;
("onchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_68_61_6E_67_65 } ;
("oncut") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_75_74 } ;
("headers") => { $ crate :: ATOM_LOCALNAME__68_65_61_64_65_72_73 } ;
("use") => { $ crate :: ATOM_LOCALNAME__75_73_65 } ;
("int") => { $ crate :: ATOM_LOCALNAME__69_6E_74 } ;
("fontstyle") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_73_74_79_6C_65 } ;
("rowspan") => { $ crate :: ATOM_LOCALNAME__72_6F_77_73_70_61_6E } ;
("switch") => { $ crate :: ATOM_LOCALNAME__73_77_69_74_63_68 } ;
("stroke-opacity") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6F_70_61_63_69_74_79 } ;
("domain") => { $ crate :: ATOM_LOCALNAME__64_6F_6D_61_69_6E } ;
("xlink:href") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_68_72_65_66 } ;
("ondragstart") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_73_74_61_72_74 } ;
("xlink:actuate") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_61_63_74_75_61_74_65 } ;
("ms") => { $ crate :: ATOM_LOCALNAME__6D_73 } ;
("oncopy") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_6F_70_79 } ;
("onscroll") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_63_72_6F_6C_6C } ;
("altglyphitem") => { $ crate :: ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68_69_74_65_6D } ;
("primitiveunits") => { $ crate :: ATOM_LOCALNAME__70_72_69_6D_69_74_69_76_65_75_6E_69_74_73 } ;
("bvar") => { $ crate :: ATOM_LOCALNAME__62_76_61_72 } ;
("mprescripts") => { $ crate :: ATOM_LOCALNAME__6D_70_72_65_73_63_72_69_70_74_73 } ;
("center") => { $ crate :: ATOM_LOCALNAME__63_65_6E_74_65_72 } ;
("min") => { $ crate :: ATOM_LOCALNAME__6D_69_6E } ;
("fontweight") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_77_65_69_67_68_74 } ;
("xmp") => { $ crate :: ATOM_LOCALNAME__78_6D_70 } ;
("equalcolumns") => { $ crate :: ATOM_LOCALNAME__65_71_75_61_6C_63_6F_6C_75_6D_6E_73 } ;
("link") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_6B } ;
("aria-valuemax") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6D_61_78 } ;
("minlength") => { $ crate :: ATOM_LOCALNAME__6D_69_6E_6C_65_6E_67_74_68 } ;
("selector") => { $ crate :: ATOM_LOCALNAME__73_65_6C_65_63_74_6F_72 } ;
("noresize") => { $ crate :: ATOM_LOCALNAME__6E_6F_72_65_73_69_7A_65 } ;
("inverse") => { $ crate :: ATOM_LOCALNAME__69_6E_76_65_72_73_65 } ;
("maxlength") => { $ crate :: ATOM_LOCALNAME__6D_61_78_6C_65_6E_67_74_68 } ;
("partialdiff") => { $ crate :: ATOM_LOCALNAME__70_61_72_74_69_61_6C_64_69_66_66 } ;
("reals") => { $ crate :: ATOM_LOCALNAME__72_65_61_6C_73 } ;
("femerge") => { $ crate :: ATOM_LOCALNAME__66_65_6D_65_72_67_65 } ;
("domainofapplication") => { $ crate :: ATOM_LOCALNAME__64_6F_6D_61_69_6E_6F_66_61_70_70_6C_69_63_61_74_69_6F_6E } ;
("xml:base") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_3A_62_61_73_65 } ;
("mfrac") => { $ crate :: ATOM_LOCALNAME__6D_66_72_61_63 } ;
("g2") => { $ crate :: ATOM_LOCALNAME__67_32 } ;
("oninput") => { $ crate :: ATOM_LOCALNAME__6F_6E_69_6E_70_75_74 } ;
("matrix") => { $ crate :: ATOM_LOCALNAME__6D_61_74_72_69_78 } ;
("surfacescale") => { $ crate :: ATOM_LOCALNAME__73_75_72_66_61_63_65_73_63_61_6C_65 } ;
("piece") => { $ crate :: ATOM_LOCALNAME__70_69_65_63_65 } ;
("power") => { $ crate :: ATOM_LOCALNAME__70_6F_77_65_72 } ;
("input") => { $ crate :: ATOM_LOCALNAME__69_6E_70_75_74 } ;
("q") => { $ crate :: ATOM_LOCALNAME__71 } ;
("repeat-template") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_2D_74_65_6D_70_6C_61_74_65 } ;
("table") => { $ crate :: ATOM_LOCALNAME__74_61_62_6C_65 } ;
("feconvolvematrix") => { $ crate :: ATOM_LOCALNAME__66_65_63_6F_6E_76_6F_6C_76_65_6D_61_74_72_69_78 } ;
("apply") => { $ crate :: ATOM_LOCALNAME__61_70_70_6C_79 } ;
("clippath") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_70_61_74_68 } ;
("http-equiv") => { $ crate :: ATOM_LOCALNAME__68_74_74_70_2D_65_71_75_69_76 } ;
("discard") => { $ crate :: ATOM_LOCALNAME__64_69_73_63_61_72_64 } ;
("complexes") => { $ crate :: ATOM_LOCALNAME__63_6F_6D_70_6C_65_78_65_73 } ;
("marker") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72 } ;
("markerWidth") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_57_69_64_74_68 } ;
("listing") => { $ crate :: ATOM_LOCALNAME__6C_69_73_74_69_6E_67 } ;
("gradientUnits") => { $ crate :: ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_55_6E_69_74_73 } ;
("aria-valuemin") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6D_69_6E } ;
("repeatcount") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_63_6F_75_6E_74 } ;
("track") => { $ crate :: ATOM_LOCALNAME__74_72_61_63_6B } ;
("groupalign") => { $ crate :: ATOM_LOCALNAME__67_72_6F_75_70_61_6C_69_67_6E } ;
("feDistantLight") => { $ crate :: ATOM_LOCALNAME__66_65_44_69_73_74_61_6E_74_4C_69_67_68_74 } ;
("maskContentUnits") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B_43_6F_6E_74_65_6E_74_55_6E_69_74_73 } ;
("arctanh") => { $ crate :: ATOM_LOCALNAME__61_72_63_74_61_6E_68 } ;
("csch") => { $ crate :: ATOM_LOCALNAME__63_73_63_68 } ;
("onbeforeactivate") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_61_63_74_69_76_61_74_65 } ;
("onreadystatechange") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_65_61_64_79_73_74_61_74_65_63_68_61_6E_67_65 } ;
("ononline") => { $ crate :: ATOM_LOCALNAME__6F_6E_6F_6E_6C_69_6E_65 } ;
("rowspacing") => { $ crate :: ATOM_LOCALNAME__72_6F_77_73_70_61_63_69_6E_67 } ;
("onbeforecut") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_63_75_74 } ;
("aria-labelledby") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6C_61_62_65_6C_6C_65_64_62_79 } ;
("superscriptshift") => { $ crate :: ATOM_LOCALNAME__73_75_70_65_72_73_63_72_69_70_74_73_68_69_66_74 } ;
("ondatasetcomplete") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_61_74_61_73_65_74_63_6F_6D_70_6C_65_74_65 } ;
("embed") => { $ crate :: ATOM_LOCALNAME__65_6D_62_65_64 } ;
("elevation") => { $ crate :: ATOM_LOCALNAME__65_6C_65_76_61_74_69_6F_6E } ;
("arctan") => { $ crate :: ATOM_LOCALNAME__61_72_63_74_61_6E } ;
("letter-spacing") => { $ crate :: ATOM_LOCALNAME__6C_65_74_74_65_72_2D_73_70_61_63_69_6E_67 } ;
("aria-sort") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_73_6F_72_74 } ;
("mtd") => { $ crate :: ATOM_LOCALNAME__6D_74_64 } ;
("keysplines") => { $ crate :: ATOM_LOCALNAME__6B_65_79_73_70_6C_69_6E_65_73 } ;
("onfinish") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_69_6E_69_73_68 } ;
("feComponentTransfer") => { $ crate :: ATOM_LOCALNAME__66_65_43_6F_6D_70_6F_6E_65_6E_74_54_72_61_6E_73_66_65_72 } ;
("autoplay") => { $ crate :: ATOM_LOCALNAME__61_75_74_6F_70_6C_61_79 } ;
("panose-1") => { $ crate :: ATOM_LOCALNAME__70_61_6E_6F_73_65_2D_31 } ;
("h5") => { $ crate :: ATOM_LOCALNAME__68_35 } ;
("scope") => { $ crate :: ATOM_LOCALNAME__73_63_6F_70_65 } ;
("determinant") => { $ crate :: ATOM_LOCALNAME__64_65_74_65_72_6D_69_6E_61_6E_74 } ;
("kernelUnitLength") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_65_6C_55_6E_69_74_4C_65_6E_67_74_68 } ;
("x1") => { $ crate :: ATOM_LOCALNAME__78_31 } ;
("aria-invalid") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_69_6E_76_61_6C_69_64 } ;
("handler") => { $ crate :: ATOM_LOCALNAME__68_61_6E_64_6C_65_72 } ;
("neq") => { $ crate :: ATOM_LOCALNAME__6E_65_71 } ;
("arccsch") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_73_63_68 } ;
("mask") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B } ;
("output") => { $ crate :: ATOM_LOCALNAME__6F_75_74_70_75_74 } ;
("usemap") => { $ crate :: ATOM_LOCALNAME__75_73_65_6D_61_70 } ;
("preserveAspectRatio") => { $ crate :: ATOM_LOCALNAME__70_72_65_73_65_72_76_65_41_73_70_65_63_74_52_61_74_69_6F } ;
("xlink:role") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_72_6F_6C_65 } ;
("feoffset") => { $ crate :: ATOM_LOCALNAME__66_65_6F_66_66_73_65_74 } ;
("xref") => { $ crate :: ATOM_LOCALNAME__78_72_65_66 } ;
("keygen") => { $ crate :: ATOM_LOCALNAME__6B_65_79_67_65_6E } ;
("onrowexit") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_6F_77_65_78_69_74 } ;
("string") => { $ crate :: ATOM_LOCALNAME__73_74_72_69_6E_67 } ;
("animatetransform") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_74_72_61_6E_73_66_6F_72_6D } ;
("v-ideographic") => { $ crate :: ATOM_LOCALNAME__76_2D_69_64_65_6F_67_72_61_70_68_69_63 } ;
("manifest") => { $ crate :: ATOM_LOCALNAME__6D_61_6E_69_66_65_73_74 } ;
("degree") => { $ crate :: ATOM_LOCALNAME__64_65_67_72_65_65 } ;
("ci") => { $ crate :: ATOM_LOCALNAME__63_69 } ;
("mstyle") => { $ crate :: ATOM_LOCALNAME__6D_73_74_79_6C_65 } ;
("abbr") => { $ crate :: ATOM_LOCALNAME__61_62_62_72 } ;
("fefuncb") => { $ crate :: ATOM_LOCALNAME__66_65_66_75_6E_63_62 } ;
("h2") => { $ crate :: ATOM_LOCALNAME__68_32 } ;
("logbase") => { $ crate :: ATOM_LOCALNAME__6C_6F_67_62_61_73_65 } ;
("alink") => { $ crate :: ATOM_LOCALNAME__61_6C_69_6E_6B } ;
("markerheight") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_68_65_69_67_68_74 } ;
("form") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D } ;
("altglyphdef") => { $ crate :: ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68_64_65_66 } ;
("display") => { $ crate :: ATOM_LOCALNAME__64_69_73_70_6C_61_79 } ;
("orient") => { $ crate :: ATOM_LOCALNAME__6F_72_69_65_6E_74 } ;
("clip") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70 } ;
("accept") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_70_74 } ;
("arabic-form") => { $ crate :: ATOM_LOCALNAME__61_72_61_62_69_63_2D_66_6F_72_6D } ;
("onlosecapture") => { $ crate :: ATOM_LOCALNAME__6F_6E_6C_6F_73_65_63_61_70_74_75_72_65 } ;
("textpath") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_70_61_74_68 } ;
("mtr") => { $ crate :: ATOM_LOCALNAME__6D_74_72 } ;
("xml:space") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_3A_73_70_61_63_65 } ;
("y2") => { $ crate :: ATOM_LOCALNAME__79_32 } ;
("menu") => { $ crate :: ATOM_LOCALNAME__6D_65_6E_75 } ;
("picture") => { $ crate :: ATOM_LOCALNAME__70_69_63_74_75_72_65 } ;
("codebase") => { $ crate :: ATOM_LOCALNAME__63_6F_64_65_62_61_73_65 } ;
("targetx") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74_78 } ;
("rationals") => { $ crate :: ATOM_LOCALNAME__72_61_74_69_6F_6E_61_6C_73 } ;
("eq") => { $ crate :: ATOM_LOCALNAME__65_71 } ;
("set") => { $ crate :: ATOM_LOCALNAME__73_65_74 } ;
("aria-multiselectable") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6D_75_6C_74_69_73_65_6C_65_63_74_61_62_6C_65 } ;
("symmetric") => { $ crate :: ATOM_LOCALNAME__73_79_6D_6D_65_74_72_69_63 } ;
("stroke-linejoin") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6C_69_6E_65_6A_6F_69_6E } ;
("minus") => { $ crate :: ATOM_LOCALNAME__6D_69_6E_75_73 } ;
("thickmathspace") => { $ crate :: ATOM_LOCALNAME__74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 } ;
("subscriptshift") => { $ crate :: ATOM_LOCALNAME__73_75_62_73_63_72_69_70_74_73_68_69_66_74 } ;
("lspace") => { $ crate :: ATOM_LOCALNAME__6C_73_70_61_63_65 } ;
("piecewise") => { $ crate :: ATOM_LOCALNAME__70_69_65_63_65_77_69_73_65 } ;
("median") => { $ crate :: ATOM_LOCALNAME__6D_65_64_69_61_6E } ;
("externalresourcesrequired") => { $ crate :: ATOM_LOCALNAME__65_78_74_65_72_6E_61_6C_72_65_73_6F_75_72_63_65_73_72_65_71_75_69_72_65_64 } ;
("ondragend") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_65_6E_64 } ;
("radius") => { $ crate :: ATOM_LOCALNAME__72_61_64_69_75_73 } ;
("refY") => { $ crate :: ATOM_LOCALNAME__72_65_66_59 } ;
("seamless") => { $ crate :: ATOM_LOCALNAME__73_65_61_6D_6C_65_73_73 } ;
("version") => { $ crate :: ATOM_LOCALNAME__76_65_72_73_69_6F_6E } ;
("em") => { $ crate :: ATOM_LOCALNAME__65_6D } ;
("contentstyletype") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_73_74_79_6C_65_74_79_70_65 } ;
("line") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65 } ;
("columnspacing") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_73_70_61_63_69_6E_67 } ;
("onmousemove") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6D_6F_76_65 } ;
("divergence") => { $ crate :: ATOM_LOCALNAME__64_69_76_65_72_67_65_6E_63_65 } ;
("onpageshow") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_61_67_65_73_68_6F_77 } ;
("tan") => { $ crate :: ATOM_LOCALNAME__74_61_6E } ;
("repeatdur") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_64_75_72 } ;
("spellcheck") => { $ crate :: ATOM_LOCALNAME__73_70_65_6C_6C_63_68_65_63_6B } ;
("feFlood") => { $ crate :: ATOM_LOCALNAME__66_65_46_6C_6F_6F_64 } ;
("feFuncR") => { $ crate :: ATOM_LOCALNAME__66_65_46_75_6E_63_52 } ;
("font-family") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_6D_69_6C_79 } ;
("onafterupdate") => { $ crate :: ATOM_LOCALNAME__6F_6E_61_66_74_65_72_75_70_64_61_74_65 } ;
("cx") => { $ crate :: ATOM_LOCALNAME__63_78 } ;
("scriptlevel") => { $ crate :: ATOM_LOCALNAME__73_63_72_69_70_74_6C_65_76_65_6C } ;
("colspan") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_73_70_61_6E } ;
("isindex") => { $ crate :: ATOM_LOCALNAME__69_73_69_6E_64_65_78 } ;
("src") => { $ crate :: ATOM_LOCALNAME__73_72_63 } ;
("aria-selected") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_73_65_6C_65_63_74_65_64 } ;
("kernelmatrix") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_65_6C_6D_61_74_72_69_78 } ;
("onmoveend") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_76_65_65_6E_64 } ;
("aria-dropeffect") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_64_72_6F_70_65_66_66_65_63_74 } ;
("fegaussianblur") => { $ crate :: ATOM_LOCALNAME__66_65_67_61_75_73_73_69_61_6E_62_6C_75_72 } ;
("stitchtiles") => { $ crate :: ATOM_LOCALNAME__73_74_69_74_63_68_74_69_6C_65_73 } ;
("required") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64 } ;
("ondragdrop") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_64_72_6F_70 } ;
("pathlength") => { $ crate :: ATOM_LOCALNAME__70_61_74_68_6C_65_6E_67_74_68 } ;
("onbegin") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_67_69_6E } ;
("nohref") => { $ crate :: ATOM_LOCALNAME__6E_6F_68_72_65_66 } ;
("horiz-adv-x") => { $ crate :: ATOM_LOCALNAME__68_6F_72_69_7A_2D_61_64_76_2D_78 } ;
("oncellchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_65_6C_6C_63_68_61_6E_67_65 } ;
("vlink") => { $ crate :: ATOM_LOCALNAME__76_6C_69_6E_6B } ;
("curl") => { $ crate :: ATOM_LOCALNAME__63_75_72_6C } ;
("figure") => { $ crate :: ATOM_LOCALNAME__66_69_67_75_72_65 } ;
("preserveaspectratio") => { $ crate :: ATOM_LOCALNAME__70_72_65_73_65_72_76_65_61_73_70_65_63_74_72_61_74_69_6F } ;
("statechange") => { $ crate :: ATOM_LOCALNAME__73_74_61_74_65_63_68_61_6E_67_65 } ;
("cosh") => { $ crate :: ATOM_LOCALNAME__63_6F_73_68 } ;
("mlabeledtr") => { $ crate :: ATOM_LOCALNAME__6D_6C_61_62_65_6C_65_64_74_72 } ;
("subset") => { $ crate :: ATOM_LOCALNAME__73_75_62_73_65_74 } ;
("color-rendering") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72_2D_72_65_6E_64_65_72_69_6E_67 } ;
("surfaceScale") => { $ crate :: ATOM_LOCALNAME__73_75_72_66_61_63_65_53_63_61_6C_65 } ;
("onbeforeupdate") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_75_70_64_61_74_65 } ;
("formaction") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_61_63_74_69_6F_6E } ;
("linearGradient") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_61_72_47_72_61_64_69_65_6E_74 } ;
("kind") => { $ crate :: ATOM_LOCALNAME__6B_69_6E_64 } ;
("transform") => { $ crate :: ATOM_LOCALNAME__74_72_61_6E_73_66_6F_72_6D } ;
("rx") => { $ crate :: ATOM_LOCALNAME__72_78 } ;
("aria-describedby") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_64_65_73_63_72_69_62_65_64_62_79 } ;
("arg") => { $ crate :: ATOM_LOCALNAME__61_72_67 } ;
("spreadMethod") => { $ crate :: ATOM_LOCALNAME__73_70_72_65_61_64_4D_65_74_68_6F_64 } ;
("profile") => { $ crate :: ATOM_LOCALNAME__70_72_6F_66_69_6C_65 } ;
("cartesianproduct") => { $ crate :: ATOM_LOCALNAME__63_61_72_74_65_73_69_61_6E_70_72_6F_64_75_63_74 } ;
("maskcontentunits") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B_63_6F_6E_74_65_6E_74_75_6E_69_74_73 } ;
("clip-rule") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_2D_72_75_6C_65 } ;
("reversed") => { $ crate :: ATOM_LOCALNAME__72_65_76_65_72_73_65_64 } ;
("font-variant") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_76_61_72_69_61_6E_74 } ;
("diffuseConstant") => { $ crate :: ATOM_LOCALNAME__64_69_66_66_75_73_65_43_6F_6E_73_74_61_6E_74 } ;
("code") => { $ crate :: ATOM_LOCALNAME__63_6F_64_65 } ;
("figcaption") => { $ crate :: ATOM_LOCALNAME__66_69_67_63_61_70_74_69_6F_6E } ;
("bevelled") => { $ crate :: ATOM_LOCALNAME__62_65_76_65_6C_6C_65_64 } ;
("mathsize") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_73_69_7A_65 } ;
("tableValues") => { $ crate :: ATOM_LOCALNAME__74_61_62_6C_65_56_61_6C_75_65_73 } ;
("color") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72 } ;
("mtext") => { $ crate :: ATOM_LOCALNAME__6D_74_65_78_74 } ;
("vkern") => { $ crate :: ATOM_LOCALNAME__76_6B_65_72_6E } ;
("close") => { $ crate :: ATOM_LOCALNAME__63_6C_6F_73_65 } ;
("body") => { $ crate :: ATOM_LOCALNAME__62_6F_64_79 } ;
("font-face-src") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_73_72_63 } ;
("repeatDur") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_44_75_72 } ;
("keyTimes") => { $ crate :: ATOM_LOCALNAME__6B_65_79_54_69_6D_65_73 } ;
("s") => { $ crate :: ATOM_LOCALNAME__73 } ;
("value") => { $ crate :: ATOM_LOCALNAME__76_61_6C_75_65 } ;
("munderover") => { $ crate :: ATOM_LOCALNAME__6D_75_6E_64_65_72_6F_76_65_72 } ;
("feColorMatrix") => { $ crate :: ATOM_LOCALNAME__66_65_43_6F_6C_6F_72_4D_61_74_72_69_78 } ;
("animation") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_69_6F_6E } ;
("columnalign") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_61_6C_69_67_6E } ;
("strike") => { $ crate :: ATOM_LOCALNAME__73_74_72_69_6B_65 } ;
("mo") => { $ crate :: ATOM_LOCALNAME__6D_6F } ;
("") => { $ crate :: ATOM_LOCALNAME_ } ;
("aria-multiline") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6D_75_6C_74_69_6C_69_6E_65 } ;
("root") => { $ crate :: ATOM_LOCALNAME__72_6F_6F_74 } ;
("font-style") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_73_74_79_6C_65 } ;
("msubsup") => { $ crate :: ATOM_LOCALNAME__6D_73_75_62_73_75_70 } ;
("repeat") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74 } ;
("textPath") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_50_61_74_68 } ;
("mathematical") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_65_6D_61_74_69_63_61_6C } ;
("baseProfile") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_50_72_6F_66_69_6C_65 } ;
("actuate") => { $ crate :: ATOM_LOCALNAME__61_63_74_75_61_74_65 } ;
("type") => { $ crate :: ATOM_LOCALNAME__74_79_70_65 } ;
("color-interpolation") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72_2D_69_6E_74_65_72_70_6F_6C_61_74_69_6F_6E } ;
("cursor") => { $ crate :: ATOM_LOCALNAME__63_75_72_73_6F_72 } ;
("font-stretch") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_73_74_72_65_74_63_68 } ;
("data") => { $ crate :: ATOM_LOCALNAME__64_61_74_61 } ;
("gradienttransform") => { $ crate :: ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_74_72_61_6E_73_66_6F_72_6D } ;
("rquote") => { $ crate :: ATOM_LOCALNAME__72_71_75_6F_74_65 } ;
("unicode-bidi") => { $ crate :: ATOM_LOCALNAME__75_6E_69_63_6F_64_65_2D_62_69_64_69 } ;
("aria-level") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6C_65_76_65_6C } ;
("onpagehide") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_61_67_65_68_69_64_65 } ;
("stretchy") => { $ crate :: ATOM_LOCALNAME__73_74_72_65_74_63_68_79 } ;
("dialog") => { $ crate :: ATOM_LOCALNAME__64_69_61_6C_6F_67 } ;
("end") => { $ crate :: ATOM_LOCALNAME__65_6E_64 } ;
("font-face-uri") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_75_72_69 } ;
("coth") => { $ crate :: ATOM_LOCALNAME__63_6F_74_68 } ;
("animatecolor") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_63_6F_6C_6F_72 } ;
("pathLength") => { $ crate :: ATOM_LOCALNAME__70_61_74_68_4C_65_6E_67_74_68 } ;
("option") => { $ crate :: ATOM_LOCALNAME__6F_70_74_69_6F_6E } ;
("calcmode") => { $ crate :: ATOM_LOCALNAME__63_61_6C_63_6D_6F_64_65 } ;
("itemtype") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_74_79_70_65 } ;
("math") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68 } ;
("tabindex") => { $ crate :: ATOM_LOCALNAME__74_61_62_69_6E_64_65_78 } ;
("marker-end") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_65_6E_64 } ;
("repeat-max") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_2D_6D_61_78 } ;
("bbox") => { $ crate :: ATOM_LOCALNAME__62_62_6F_78 } ;
("cos") => { $ crate :: ATOM_LOCALNAME__63_6F_73 } ;
("fontsize") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_73_69_7A_65 } ;
("definition-src") => { $ crate :: ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_2D_73_72_63 } ;
("fy") => { $ crate :: ATOM_LOCALNAME__66_79 } ;
("feimage") => { $ crate :: ATOM_LOCALNAME__66_65_69_6D_61_67_65 } ;
("menuitem") => { $ crate :: ATOM_LOCALNAME__6D_65_6E_75_69_74_65_6D } ;
("checked") => { $ crate :: ATOM_LOCALNAME__63_68_65_63_6B_65_64 } ;
("v-alphabetic") => { $ crate :: ATOM_LOCALNAME__76_2D_61_6C_70_68_61_62_65_74_69_63 } ;
("refy") => { $ crate :: ATOM_LOCALNAME__72_65_66_79 } ;
("accent") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_6E_74 } ;
("onfocusin") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_63_75_73_69_6E } ;
("glyph-name") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_2D_6E_61_6D_65 } ;
("sup") => { $ crate :: ATOM_LOCALNAME__73_75_70 } ;
("bdi") => { $ crate :: ATOM_LOCALNAME__62_64_69 } ;
("arccos") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_6F_73 } ;
("condition") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_64_69_74_69_6F_6E } ;
("interval") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_72_76_61_6C } ;
("other") => { $ crate :: ATOM_LOCALNAME__6F_74_68_65_72 } ;
("semantics") => { $ crate :: ATOM_LOCALNAME__73_65_6D_61_6E_74_69_63_73 } ;
("visibility") => { $ crate :: ATOM_LOCALNAME__76_69_73_69_62_69_6C_69_74_79 } ;
("aria-haspopup") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_68_61_73_70_6F_70_75_70 } ;
("aria-channel") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_63_68_61_6E_6E_65_6C } ;
("feFuncB") => { $ crate :: ATOM_LOCALNAME__66_65_46_75_6E_63_42 } ;
("repeatCount") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_43_6F_75_6E_74 } ;
("object") => { $ crate :: ATOM_LOCALNAME__6F_62_6A_65_63_74 } ;
("g1") => { $ crate :: ATOM_LOCALNAME__67_31 } ;
("pi") => { $ crate :: ATOM_LOCALNAME__70_69 } ;
("pointsatz") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_7A } ;
("hidden") => { $ crate :: ATOM_LOCALNAME__68_69_64_64_65_6E } ;
("basefrequency") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_66_72_65_71_75_65_6E_63_79 } ;
("id") => { $ crate :: ATOM_LOCALNAME__69_64 } ;
("startoffset") => { $ crate :: ATOM_LOCALNAME__73_74_61_72_74_6F_66_66_73_65_74 } ;
("del") => { $ crate :: ATOM_LOCALNAME__64_65_6C } ;
("when") => { $ crate :: ATOM_LOCALNAME__77_68_65_6E } ;
("maskunits") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B_75_6E_69_74_73 } ;
("exponentiale") => { $ crate :: ATOM_LOCALNAME__65_78_70_6F_6E_65_6E_74_69_61_6C_65 } ;
("muted") => { $ crate :: ATOM_LOCALNAME__6D_75_74_65_64 } ;
("annotation") => { $ crate :: ATOM_LOCALNAME__61_6E_6E_6F_74_61_74_69_6F_6E } ;
("filter") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72 } ;
("thead") => { $ crate :: ATOM_LOCALNAME__74_68_65_61_64 } ;
("tr") => { $ crate :: ATOM_LOCALNAME__74_72 } ;
("autofocus") => { $ crate :: ATOM_LOCALNAME__61_75_74_6F_66_6F_63_75_73 } ;
("limitingconeangle") => { $ crate :: ATOM_LOCALNAME__6C_69_6D_69_74_69_6E_67_63_6F_6E_65_61_6E_67_6C_65 } ;
("xlink:show") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_73_68_6F_77 } ;
("itemscope") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_73_63_6F_70_65 } ;
("polyline") => { $ crate :: ATOM_LOCALNAME__70_6F_6C_79_6C_69_6E_65 } ;
("main") => { $ crate :: ATOM_LOCALNAME__6D_61_69_6E } ;
("sizes") => { $ crate :: ATOM_LOCALNAME__73_69_7A_65_73 } ;
("animate") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65 } ;
("dy") => { $ crate :: ATOM_LOCALNAME__64_79 } ;
("maction") => { $ crate :: ATOM_LOCALNAME__6D_61_63_74_69_6F_6E } ;
("loop") => { $ crate :: ATOM_LOCALNAME__6C_6F_6F_70 } ;
("onmessage") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_65_73_73_61_67_65 } ;
("edgeMode") => { $ crate :: ATOM_LOCALNAME__65_64_67_65_4D_6F_64_65 } ;
("arccoth") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_6F_74_68 } ;
("fill-rule") => { $ crate :: ATOM_LOCALNAME__66_69_6C_6C_2D_72_75_6C_65 } ;
("contentScriptType") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_53_63_72_69_70_74_54_79_70_65 } ;
("exists") => { $ crate :: ATOM_LOCALNAME__65_78_69_73_74_73 } ;
("gt") => { $ crate :: ATOM_LOCALNAME__67_74 } ;
("closure") => { $ crate :: ATOM_LOCALNAME__63_6C_6F_73_75_72_65 } ;
("datatemplate") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_74_65_6D_70_6C_61_74_65 } ;
("tanh") => { $ crate :: ATOM_LOCALNAME__74_61_6E_68 } ;
("clippathunits") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_70_61_74_68_75_6E_69_74_73 } ;
("preload") => { $ crate :: ATOM_LOCALNAME__70_72_65_6C_6F_61_64 } ;
("controls") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_72_6F_6C_73 } ;
("href") => { $ crate :: ATOM_LOCALNAME__68_72_65_66 } ;
("specularExponent") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_45_78_70_6F_6E_65_6E_74 } ;
("itemref") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_72_65_66 } ;
("xlink:type") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_74_79_70_65 } ;
("lowlimit") => { $ crate :: ATOM_LOCALNAME__6C_6F_77_6C_69_6D_69_74 } ;
("defs") => { $ crate :: ATOM_LOCALNAME__64_65_66_73 } ;
("ul") => { $ crate :: ATOM_LOCALNAME__75_6C } ;
("vert-origin-x") => { $ crate :: ATOM_LOCALNAME__76_65_72_74_2D_6F_72_69_67_69_6E_2D_78 } ;
("lighting-color") => { $ crate :: ATOM_LOCALNAME__6C_69_67_68_74_69_6E_67_2D_63_6F_6C_6F_72 } ;
("arcsec") => { $ crate :: ATOM_LOCALNAME__61_72_63_73_65_63 } ;
("desc") => { $ crate :: ATOM_LOCALNAME__64_65_73_63 } ;
("origin") => { $ crate :: ATOM_LOCALNAME__6F_72_69_67_69_6E } ;
("veryverythickmathspace") => { $ crate :: ATOM_LOCALNAME__76_65_72_79_76_65_72_79_74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 } ;
("rows") => { $ crate :: ATOM_LOCALNAME__72_6F_77_73 } ;
("repeat-min") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_2D_6D_69_6E } ;
("high") => { $ crate :: ATOM_LOCALNAME__68_69_67_68 } ;
("format") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_61_74 } ;
("notsubset") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_73_75_62_73_65_74 } ;
("scale") => { $ crate :: ATOM_LOCALNAME__73_63_61_6C_65 } ;
("arcsinh") => { $ crate :: ATOM_LOCALNAME__61_72_63_73_69_6E_68 } ;
("arcrole") => { $ crate :: ATOM_LOCALNAME__61_72_63_72_6F_6C_65 } ;
("mrow") => { $ crate :: ATOM_LOCALNAME__6D_72_6F_77 } ;
("image-rendering") => { $ crate :: ATOM_LOCALNAME__69_6D_61_67_65_2D_72_65_6E_64_65_72_69_6E_67 } ;
("onfilterchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_69_6C_74_65_72_63_68_61_6E_67_65 } ;
("feSpotLight") => { $ crate :: ATOM_LOCALNAME__66_65_53_70_6F_74_4C_69_67_68_74 } ;
("font-face") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65 } ;
("specularconstant") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_63_6F_6E_73_74_61_6E_74 } ;
("largeop") => { $ crate :: ATOM_LOCALNAME__6C_61_72_67_65_6F_70 } ;
("fill") => { $ crate :: ATOM_LOCALNAME__66_69_6C_6C } ;
("onmove") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_76_65 } ;
("ol") => { $ crate :: ATOM_LOCALNAME__6F_6C } ;
("source") => { $ crate :: ATOM_LOCALNAME__73_6F_75_72_63_65 } ;
("onabort") => { $ crate :: ATOM_LOCALNAME__6F_6E_61_62_6F_72_74 } ;
("aria-controls") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_63_6F_6E_74_72_6F_6C_73 } ;
("xchannelselector") => { $ crate :: ATOM_LOCALNAME__78_63_68_61_6E_6E_65_6C_73_65_6C_65_63_74_6F_72 } ;
("symbol") => { $ crate :: ATOM_LOCALNAME__73_79_6D_62_6F_6C } ;
("quotient") => { $ crate :: ATOM_LOCALNAME__71_75_6F_74_69_65_6E_74 } ;
("lineargradient") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_61_72_67_72_61_64_69_65_6E_74 } ;
("rb") => { $ crate :: ATOM_LOCALNAME__72_62 } ;
("toggle") => { $ crate :: ATOM_LOCALNAME__74_6F_67_67_6C_65 } ;
("color-interpolation-filters") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72_2D_69_6E_74_65_72_70_6F_6C_61_74_69_6F_6E_2D_66_69_6C_74_65_72_73 } ;
("default") => { $ crate :: ATOM_LOCALNAME__64_65_66_61_75_6C_74 } ;
("accesskey") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_73_73_6B_65_79 } ;
("font-size") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_73_69_7A_65 } ;
("mphantom") => { $ crate :: ATOM_LOCALNAME__6D_70_68_61_6E_74_6F_6D } ;
("tablevalues") => { $ crate :: ATOM_LOCALNAME__74_61_62_6C_65_76_61_6C_75_65_73 } ;
("stemh") => { $ crate :: ATOM_LOCALNAME__73_74_65_6D_68 } ;
("onbefordeactivate") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_64_65_61_63_74_69_76_61_74_65 } ;
("onunload") => { $ crate :: ATOM_LOCALNAME__6F_6E_75_6E_6C_6F_61_64 } ;
("feFuncA") => { $ crate :: ATOM_LOCALNAME__66_65_46_75_6E_63_41 } ;
("sec") => { $ crate :: ATOM_LOCALNAME__73_65_63 } ;
("viewTarget") => { $ crate :: ATOM_LOCALNAME__76_69_65_77_54_61_72_67_65_74 } ;
("view") => { $ crate :: ATOM_LOCALNAME__76_69_65_77 } ;
("srcset") => { $ crate :: ATOM_LOCALNAME__73_72_63_73_65_74 } ;
("poster") => { $ crate :: ATOM_LOCALNAME__70_6F_73_74_65_72 } ;
("audio") => { $ crate :: ATOM_LOCALNAME__61_75_64_69_6F } ;
("orientation") => { $ crate :: ATOM_LOCALNAME__6F_72_69_65_6E_74_61_74_69_6F_6E } ;
("vector") => { $ crate :: ATOM_LOCALNAME__76_65_63_74_6F_72 } ;
("amplitude") => { $ crate :: ATOM_LOCALNAME__61_6D_70_6C_69_74_75_64_65 } ;
("a") => { $ crate :: ATOM_LOCALNAME__61 } ;
("rules") => { $ crate :: ATOM_LOCALNAME__72_75_6C_65_73 } ;
("placeholder") => { $ crate :: ATOM_LOCALNAME__70_6C_61_63_65_68_6F_6C_64_65_72 } ;
("clip-path") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_2D_70_61_74_68 } ;
("feGaussianBlur") => { $ crate :: ATOM_LOCALNAME__66_65_47_61_75_73_73_69_61_6E_42_6C_75_72 } ;
("conjugate") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_6A_75_67_61_74_65 } ;
("polygon") => { $ crate :: ATOM_LOCALNAME__70_6F_6C_79_67_6F_6E } ;
("mroot") => { $ crate :: ATOM_LOCALNAME__6D_72_6F_6F_74 } ;
("arcsech") => { $ crate :: ATOM_LOCALNAME__61_72_63_73_65_63_68 } ;
("maligngroup") => { $ crate :: ATOM_LOCALNAME__6D_61_6C_69_67_6E_67_72_6F_75_70 } ;
("in") => { $ crate :: ATOM_LOCALNAME__69_6E } ;
("color-profile") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72_2D_70_72_6F_66_69_6C_65 } ;
("standby") => { $ crate :: ATOM_LOCALNAME__73_74_61_6E_64_62_79 } ;
("ry") => { $ crate :: ATOM_LOCALNAME__72_79 } ;
("span") => { $ crate :: ATOM_LOCALNAME__73_70_61_6E } ;
("nobr") => { $ crate :: ATOM_LOCALNAME__6E_6F_62_72 } ;
("specularexponent") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_65_78_70_6F_6E_65_6E_74 } ;
("accentunder") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_6E_74_75_6E_64_65_72 } ;
("aria-disabled") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_64_69_73_61_62_6C_65_64 } ;
("ruby") => { $ crate :: ATOM_LOCALNAME__72_75_62_79 } ;
("dd") => { $ crate :: ATOM_LOCALNAME__64_64 } ;
("imaginary") => { $ crate :: ATOM_LOCALNAME__69_6D_61_67_69_6E_61_72_79 } ;
("area") => { $ crate :: ATOM_LOCALNAME__61_72_65_61 } ;
("verythinmathspace") => { $ crate :: ATOM_LOCALNAME__76_65_72_79_74_68_69_6E_6D_61_74_68_73_70_61_63_65 } ;
("fence") => { $ crate :: ATOM_LOCALNAME__66_65_6E_63_65 } ;
("patternContentUnits") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_43_6F_6E_74_65_6E_74_55_6E_69_74_73 } ;
("order") => { $ crate :: ATOM_LOCALNAME__6F_72_64_65_72 } ;
("exp") => { $ crate :: ATOM_LOCALNAME__65_78_70 } ;
("onbeforeeditfocus") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_65_64_69_74_66_6F_63_75_73 } ;
("enctype") => { $ crate :: ATOM_LOCALNAME__65_6E_63_74_79_70_65 } ;
("sep") => { $ crate :: ATOM_LOCALNAME__73_65_70 } ;
("requiredfeatures") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64_66_65_61_74_75_72_65_73 } ;
("animateMotion") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_4D_6F_74_69_6F_6E } ;
("r") => { $ crate :: ATOM_LOCALNAME__72 } ;
("itemid") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_69_64 } ;
("xlink:arcrole") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_61_72_63_72_6F_6C_65 } ;
("fedropshadow") => { $ crate :: ATOM_LOCALNAME__66_65_64_72_6F_70_73_68_61_64_6F_77 } ;
("fetch") => { $ crate :: ATOM_LOCALNAME__66_65_74_63_68 } ;
("text-decoration") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_2D_64_65_63_6F_72_61_74_69_6F_6E } ;
("onfocus") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_63_75_73 } ;
("vert-adv-y") => { $ crate :: ATOM_LOCALNAME__76_65_72_74_2D_61_64_76_2D_79 } ;
("formtarget") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_74_61_72_67_65_74 } ;
("blink") => { $ crate :: ATOM_LOCALNAME__62_6C_69_6E_6B } ;
("scoped") => { $ crate :: ATOM_LOCALNAME__73_63_6F_70_65_64 } ;
("moment") => { $ crate :: ATOM_LOCALNAME__6D_6F_6D_65_6E_74 } ;
("method") => { $ crate :: ATOM_LOCALNAME__6D_65_74_68_6F_64 } ;
("filterUnits") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72_55_6E_69_74_73 } ;
("mean") => { $ crate :: ATOM_LOCALNAME__6D_65_61_6E } ;
("lcm") => { $ crate :: ATOM_LOCALNAME__6C_63_6D } ;
("strong") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6E_67 } ;
("equalrows") => { $ crate :: ATOM_LOCALNAME__65_71_75_61_6C_72_6F_77_73 } ;
("onselectstart") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_65_6C_65_63_74_73_74_61_72_74 } ;
("local") => { $ crate :: ATOM_LOCALNAME__6C_6F_63_61_6C } ;
("feDropShadow") => { $ crate :: ATOM_LOCALNAME__66_65_44_72_6F_70_53_68_61_64_6F_77 } ;
("onmousedown") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_64_6F_77_6E } ;
("gcd") => { $ crate :: ATOM_LOCALNAME__67_63_64 } ;
("multicol") => { $ crate :: ATOM_LOCALNAME__6D_75_6C_74_69_63_6F_6C } ;
("h6") => { $ crate :: ATOM_LOCALNAME__68_36 } ;
("stop") => { $ crate :: ATOM_LOCALNAME__73_74_6F_70 } ;
("ondrop") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_6F_70 } ;
("arccsc") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_73_63 } ;
("onzoom") => { $ crate :: ATOM_LOCALNAME__6F_6E_7A_6F_6F_6D } ;
("clear") => { $ crate :: ATOM_LOCALNAME__63_6C_65_61_72 } ;
("sub") => { $ crate :: ATOM_LOCALNAME__73_75_62 } ;
("itemprop") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_70_72_6F_70 } ;
("scalarproduct") => { $ crate :: ATOM_LOCALNAME__73_63_61_6C_61_72_70_72_6F_64_75_63_74 } ;
("onsubmit") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_75_62_6D_69_74 } ;
("feDisplacementMap") => { $ crate :: ATOM_LOCALNAME__66_65_44_69_73_70_6C_61_63_65_6D_65_6E_74_4D_61_70 } ;
("feTurbulence") => { $ crate :: ATOM_LOCALNAME__66_65_54_75_72_62_75_6C_65_6E_63_65 } ;
("outerproduct") => { $ crate :: ATOM_LOCALNAME__6F_75_74_65_72_70_72_6F_64_75_63_74 } ;
("allowfullscreen") => { $ crate :: ATOM_LOCALNAME__61_6C_6C_6F_77_66_75_6C_6C_73_63_72_65_65_6E } ;
("label") => { $ crate :: ATOM_LOCALNAME__6C_61_62_65_6C } ;
("sdev") => { $ crate :: ATOM_LOCALNAME__73_64_65_76 } ;
("exponent") => { $ crate :: ATOM_LOCALNAME__65_78_70_6F_6E_65_6E_74 } ;
("speed") => { $ crate :: ATOM_LOCALNAME__73_70_65_65_64 } ;
("font-face-name") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_6E_61_6D_65 } ;
("descent") => { $ crate :: ATOM_LOCALNAME__64_65_73_63_65_6E_74 } ;
("font") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74 } ;
("applet") => { $ crate :: ATOM_LOCALNAME__61_70_70_6C_65_74 } ;
("feturbulence") => { $ crate :: ATOM_LOCALNAME__66_65_74_75_72_62_75_6C_65_6E_63_65 } ;
("fontfamily") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_66_61_6D_69_6C_79 } ;
("pattern") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E } ;
("article") => { $ crate :: ATOM_LOCALNAME__61_72_74_69_63_6C_65 } ;
("active") => { $ crate :: ATOM_LOCALNAME__61_63_74_69_76_65 } ;
("referrerpolicy") => { $ crate :: ATOM_LOCALNAME__72_65_66_65_72_72_65_72_70_6F_6C_69_63_79 } ;
("refx") => { $ crate :: ATOM_LOCALNAME__72_65_66_78 } ;
("vectorproduct") => { $ crate :: ATOM_LOCALNAME__76_65_63_74_6F_72_70_72_6F_64_75_63_74 } ;
("card") => { $ crate :: ATOM_LOCALNAME__63_61_72_64 } ;
("rotate") => { $ crate :: ATOM_LOCALNAME__72_6F_74_61_74_65 } ;
("oncontrolselect") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_6F_6E_74_72_6F_6C_73_65_6C_65_63_74 } ;
("maskUnits") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B_55_6E_69_74_73 } ;
("rev") => { $ crate :: ATOM_LOCALNAME__72_65_76 } ;
("fefunca") => { $ crate :: ATOM_LOCALNAME__66_65_66_75_6E_63_61 } ;
("rendering-intent") => { $ crate :: ATOM_LOCALNAME__72_65_6E_64_65_72_69_6E_67_2D_69_6E_74_65_6E_74 } ;
("unicode") => { $ crate :: ATOM_LOCALNAME__75_6E_69_63_6F_64_65 } ;
("ismap") => { $ crate :: ATOM_LOCALNAME__69_73_6D_61_70 } ;
("onreset") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_65_73_65_74 } ;
("samp") => { $ crate :: ATOM_LOCALNAME__73_61_6D_70 } ;
("emptyset") => { $ crate :: ATOM_LOCALNAME__65_6D_70_74_79_73_65_74 } ;
("ondragenter") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_65_6E_74_65_72 } ;
("event") => { $ crate :: ATOM_LOCALNAME__65_76_65_6E_74 } ;
("lquote") => { $ crate :: ATOM_LOCALNAME__6C_71_75_6F_74_65 } ;
("html") => { $ crate :: ATOM_LOCALNAME__68_74_6D_6C } ;
("columnwidth") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_77_69_64_74_68 } ;
("opacity") => { $ crate :: ATOM_LOCALNAME__6F_70_61_63_69_74_79 } ;
("decoding") => { $ crate :: ATOM_LOCALNAME__64_65_63_6F_64_69_6E_67 } ;
("nest") => { $ crate :: ATOM_LOCALNAME__6E_65_73_74 } ;
("offset") => { $ crate :: ATOM_LOCALNAME__6F_66_66_73_65_74 } ;
("systemLanguage") => { $ crate :: ATOM_LOCALNAME__73_79_73_74_65_6D_4C_61_6E_67_75_61_67_65 } ;
("cols") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_73 } ;
("naturalnumbers") => { $ crate :: ATOM_LOCALNAME__6E_61_74_75_72_61_6C_6E_75_6D_62_65_72_73 } ;
("mtable") => { $ crate :: ATOM_LOCALNAME__6D_74_61_62_6C_65 } ;
("valuetype") => { $ crate :: ATOM_LOCALNAME__76_61_6C_75_65_74_79_70_65 } ;
("target") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74 } ;
("maxsize") => { $ crate :: ATOM_LOCALNAME__6D_61_78_73_69_7A_65 } ;
("noscript") => { $ crate :: ATOM_LOCALNAME__6E_6F_73_63_72_69_70_74 } ;
("aria-posinset") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_70_6F_73_69_6E_73_65_74 } ;
("style") => { $ crate :: ATOM_LOCALNAME__73_74_79_6C_65 } ;
("inputmode") => { $ crate :: ATOM_LOCALNAME__69_6E_70_75_74_6D_6F_64_65 } ;
("aria-grab") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_67_72_61_62 } ;
("writing-mode") => { $ crate :: ATOM_LOCALNAME__77_72_69_74_69_6E_67_2D_6D_6F_64_65 } ;
("aria-datatype") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_64_61_74_61_74_79_70_65 } ;
("frame") => { $ crate :: ATOM_LOCALNAME__66_72_61_6D_65 } ;
("dx") => { $ crate :: ATOM_LOCALNAME__64_78 } ;
("x2") => { $ crate :: ATOM_LOCALNAME__78_32 } ;
("real") => { $ crate :: ATOM_LOCALNAME__72_65_61_6C } ;
("stroke") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65 } ;
("onpaste") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_61_73_74_65 } ;
("mpath") => { $ crate :: ATOM_LOCALNAME__6D_70_61_74_68 } ;
("mediummathspace") => { $ crate :: ATOM_LOCALNAME__6D_65_64_69_75_6D_6D_61_74_68_73_70_61_63_65 } ;
("ident") => { $ crate :: ATOM_LOCALNAME__69_64_65_6E_74 } ;
("datetime") => { $ crate :: ATOM_LOCALNAME__64_61_74_65_74_69_6D_65 } ;
("div") => { $ crate :: ATOM_LOCALNAME__64_69_76 } ;
("eulergamma") => { $ crate :: ATOM_LOCALNAME__65_75_6C_65_72_67_61_6D_6D_61 } ;
("csc") => { $ crate :: ATOM_LOCALNAME__63_73_63 } ;
("k") => { $ crate :: ATOM_LOCALNAME__6B } ;
("false") => { $ crate :: ATOM_LOCALNAME__66_61_6C_73_65 } ;
("accent-height") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_6E_74_2D_68_65_69_67_68_74 } ;
("hreflang") => { $ crate :: ATOM_LOCALNAME__68_72_65_66_6C_61_6E_67 } ;
("k2") => { $ crate :: ATOM_LOCALNAME__6B_32 } ;
("viewtarget") => { $ crate :: ATOM_LOCALNAME__76_69_65_77_74_61_72_67_65_74 } ;
("gradientTransform") => { $ crate :: ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_54_72_61_6E_73_66_6F_72_6D } ;
("separator") => { $ crate :: ATOM_LOCALNAME__73_65_70_61_72_61_74_6F_72 } ;
("defer") => { $ crate :: ATOM_LOCALNAME__64_65_66_65_72 } ;
("from") => { $ crate :: ATOM_LOCALNAME__66_72_6F_6D } ;
("datafld") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_66_6C_64 } ;
("glyph-orientation-horizontal") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_2D_6F_72_69_65_6E_74_61_74_69_6F_6E_2D_68_6F_72_69_7A_6F_6E_74_61_6C } ;
("numOctaves") => { $ crate :: ATOM_LOCALNAME__6E_75_6D_4F_63_74_61_76_65_73 } ;
("mover") => { $ crate :: ATOM_LOCALNAME__6D_6F_76_65_72 } ;
("nowrap") => { $ crate :: ATOM_LOCALNAME__6E_6F_77_72_61_70 } ;
("radialGradient") => { $ crate :: ATOM_LOCALNAME__72_61_64_69_61_6C_47_72_61_64_69_65_6E_74 } ;
("notin") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_69_6E } ;
("font-size-adjust") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_73_69_7A_65_2D_61_64_6A_75_73_74 } ;
("cellpadding") => { $ crate :: ATOM_LOCALNAME__63_65_6C_6C_70_61_64_64_69_6E_67 } ;
("markerUnits") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_55_6E_69_74_73 } ;
("dt") => { $ crate :: ATOM_LOCALNAME__64_74 } ;
("floor") => { $ crate :: ATOM_LOCALNAME__66_6C_6F_6F_72 } ;
("stop-color") => { $ crate :: ATOM_LOCALNAME__73_74_6F_70_2D_63_6F_6C_6F_72 } ;
("footer") => { $ crate :: ATOM_LOCALNAME__66_6F_6F_74_65_72 } ;
("selection") => { $ crate :: ATOM_LOCALNAME__73_65_6C_65_63_74_69_6F_6E } ;
("stroke-dashoffset") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_64_61_73_68_6F_66_66_73_65_74 } ;
("property") => { $ crate :: ATOM_LOCALNAME__70_72_6F_70_65_72_74_79 } ;
("primes") => { $ crate :: ATOM_LOCALNAME__70_72_69_6D_65_73 } ;
("onstorage") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_74_6F_72_61_67_65 } ;
("onmouseup") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_75_70 } ;
("media") => { $ crate :: ATOM_LOCALNAME__6D_65_64_69_61 } ;
("summary") => { $ crate :: ATOM_LOCALNAME__73_75_6D_6D_61_72_79 } ;
("mode") => { $ crate :: ATOM_LOCALNAME__6D_6F_64_65 } ;
("nargs") => { $ crate :: ATOM_LOCALNAME__6E_61_72_67_73 } ;
("novalidate") => { $ crate :: ATOM_LOCALNAME__6E_6F_76_61_6C_69_64_61_74_65 } ;
("integrity") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_67_72_69_74_79 } ;
("dominant-baseline") => { $ crate :: ATOM_LOCALNAME__64_6F_6D_69_6E_61_6E_74_2D_62_61_73_65_6C_69_6E_65 } ;
("mathcolor") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_63_6F_6C_6F_72 } ;
("icon") => { $ crate :: ATOM_LOCALNAME__69_63_6F_6E } ;
("open") => { $ crate :: ATOM_LOCALNAME__6F_70_65_6E } ;
("aria-autocomplete") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_61_75_74_6F_63_6F_6D_70_6C_65_74_65 } ;
("optgroup") => { $ crate :: ATOM_LOCALNAME__6F_70_74_67_72_6F_75_70 } ;
("dfn") => { $ crate :: ATOM_LOCALNAME__64_66_6E } ;
("acronym") => { $ crate :: ATOM_LOCALNAME__61_63_72_6F_6E_79_6D } ;
("align") => { $ crate :: ATOM_LOCALNAME__61_6C_69_67_6E } ;
("radiogroup") => { $ crate :: ATOM_LOCALNAME__72_61_64_69_6F_67_72_6F_75_70 } ;
("tref") => { $ crate :: ATOM_LOCALNAME__74_72_65_66 } ;
("aria-pressed") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_70_72_65_73_73_65_64 } ;
("true") => { $ crate :: ATOM_LOCALNAME__74_72_75_65 } ;
("hkern") => { $ crate :: ATOM_LOCALNAME__68_6B_65_72_6E } ;
("integers") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_67_65_72_73 } ;
("aria-flowto") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_66_6C_6F_77_74_6F } ;
("rel") => { $ crate :: ATOM_LOCALNAME__72_65_6C } ;
("contenteditable") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_65_64_69_74_61_62_6C_65 } ;
("rt") => { $ crate :: ATOM_LOCALNAME__72_74 } ;
("aria-expanded") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_65_78_70_61_6E_64_65_64 } ;
("tspan") => { $ crate :: ATOM_LOCALNAME__74_73_70_61_6E } ;
("fedisplacementmap") => { $ crate :: ATOM_LOCALNAME__66_65_64_69_73_70_6C_61_63_65_6D_65_6E_74_6D_61_70 } ;
("k4") => { $ crate :: ATOM_LOCALNAME__6B_34 } ;
("to") => { $ crate :: ATOM_LOCALNAME__74_6F } ;
("foreignobject") => { $ crate :: ATOM_LOCALNAME__66_6F_72_65_69_67_6E_6F_62_6A_65_63_74 } ;
("parse") => { $ crate :: ATOM_LOCALNAME__70_61_72_73_65 } ;
("definitionURL") => { $ crate :: ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_55_52_4C } ;
("menclose") => { $ crate :: ATOM_LOCALNAME__6D_65_6E_63_6C_6F_73_65 } ;
("onmouseenter") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_65_6E_74_65_72 } ;
("archive") => { $ crate :: ATOM_LOCALNAME__61_72_63_68_69_76_65 } ;
("hr") => { $ crate :: ATOM_LOCALNAME__68_72 } ;
("compose") => { $ crate :: ATOM_LOCALNAME__63_6F_6D_70_6F_73_65 } ;
("fill-opacity") => { $ crate :: ATOM_LOCALNAME__66_69_6C_6C_2D_6F_70_61_63_69_74_79 } ;
("mn") => { $ crate :: ATOM_LOCALNAME__6D_6E } ;
("sech") => { $ crate :: ATOM_LOCALNAME__73_65_63_68 } ;
("additive") => { $ crate :: ATOM_LOCALNAME__61_64_64_69_74_69_76_65 } ;
("msub") => { $ crate :: ATOM_LOCALNAME__6D_73_75_62 } ;
("horiz-origin-y") => { $ crate :: ATOM_LOCALNAME__68_6F_72_69_7A_2D_6F_72_69_67_69_6E_2D_79 } ;
("border") => { $ crate :: ATOM_LOCALNAME__62_6F_72_64_65_72 } ;
("bias") => { $ crate :: ATOM_LOCALNAME__62_69_61_73 } ;
("font-face-format") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_66_6F_72_6D_61_74 } ;
("accept-charset") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_70_74_2D_63_68_61_72_73_65_74 } ;
("framespacing") => { $ crate :: ATOM_LOCALNAME__66_72_61_6D_65_73_70_61_63_69_6E_67 } ;
("fepointlight") => { $ crate :: ATOM_LOCALNAME__66_65_70_6F_69_6E_74_6C_69_67_68_74 } ;
("requiredFeatures") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64_46_65_61_74_75_72_65_73 } ;
("clipPath") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_50_61_74_68 } ;
("feComposite") => { $ crate :: ATOM_LOCALNAME__66_65_43_6F_6D_70_6F_73_69_74_65 } ;
("animatemotion") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_6D_6F_74_69_6F_6E } ;
("lt") => { $ crate :: ATOM_LOCALNAME__6C_74 } ;
("onbeforeunload") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_75_6E_6C_6F_61_64 } ;
("altGlyph") => { $ crate :: ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68 } ;
("role") => { $ crate :: ATOM_LOCALNAME__72_6F_6C_65 } ;
("feMerge") => { $ crate :: ATOM_LOCALNAME__66_65_4D_65_72_67_65 } ;
("edge") => { $ crate :: ATOM_LOCALNAME__65_64_67_65 } ;
("onforminput") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_72_6D_69_6E_70_75_74 } ;
("pointsAtY") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_59 } ;
("li") => { $ crate :: ATOM_LOCALNAME__6C_69 } ;
("vspace") => { $ crate :: ATOM_LOCALNAME__76_73_70_61_63_65 } ;
("multiple") => { $ crate :: ATOM_LOCALNAME__6D_75_6C_74_69_70_6C_65 } ;
("merror") => { $ crate :: ATOM_LOCALNAME__6D_65_72_72_6F_72 } ;
("aria-activedescendant") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_61_63_74_69_76_65_64_65_73_63_65_6E_64_61_6E_74 } ;
("onhashchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_68_61_73_68_63_68_61_6E_67_65 } ;
("prsubset") => { $ crate :: ATOM_LOCALNAME__70_72_73_75_62_73_65_74 } ;
("baseline") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_6C_69_6E_65 } ;
("td") => { $ crate :: ATOM_LOCALNAME__74_64 } ;
("rp") => { $ crate :: ATOM_LOCALNAME__72_70 } ;
("factorof") => { $ crate :: ATOM_LOCALNAME__66_61_63_74_6F_72_6F_66 } ;
("oncontextmenu") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_6F_6E_74_65_78_74_6D_65_6E_75 } ;
("low") => { $ crate :: ATOM_LOCALNAME__6C_6F_77 } ;
("draggable") => { $ crate :: ATOM_LOCALNAME__64_72_61_67_67_61_62_6C_65 } ;
("onpopstate") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_6F_70_73_74_61_74_65 } ;
("occurrence") => { $ crate :: ATOM_LOCALNAME__6F_63_63_75_72_72_65_6E_63_65 } ;
("time") => { $ crate :: ATOM_LOCALNAME__74_69_6D_65 } ;
("targetY") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74_59 } ;
("width") => { $ crate :: ATOM_LOCALNAME__77_69_64_74_68 } ;
("fieldset") => { $ crate :: ATOM_LOCALNAME__66_69_65_6C_64_73_65_74 } ;
("startOffset") => { $ crate :: ATOM_LOCALNAME__73_74_61_72_74_4F_66_66_73_65_74 } ;
("otherwise") => { $ crate :: ATOM_LOCALNAME__6F_74_68_65_72_77_69_73_65 } ;
("charoff") => { $ crate :: ATOM_LOCALNAME__63_68_61_72_6F_66_66 } ;
("language") => { $ crate :: ATOM_LOCALNAME__6C_61_6E_67_75_61_67_65 } ;
("limit") => { $ crate :: ATOM_LOCALNAME__6C_69_6D_69_74 } ;
("plaintext") => { $ crate :: ATOM_LOCALNAME__70_6C_61_69_6E_74_65_78_74 } ;
("ondragover") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_6F_76_65_72 } ;
("shape-rendering") => { $ crate :: ATOM_LOCALNAME__73_68_61_70_65_2D_72_65_6E_64_65_72_69_6E_67 } ;
("blockquote") => { $ crate :: ATOM_LOCALNAME__62_6C_6F_63_6B_71_75_6F_74_65 } ;
("feblend") => { $ crate :: ATOM_LOCALNAME__66_65_62_6C_65_6E_64 } ;
("onkeydown") => { $ crate :: ATOM_LOCALNAME__6F_6E_6B_65_79_64_6F_77_6E } ;
("image") => { $ crate :: ATOM_LOCALNAME__69_6D_61_67_65 } ;
("fn") => { $ crate :: ATOM_LOCALNAME__66_6E } ;
("nomodule") => { $ crate :: ATOM_LOCALNAME__6E_6F_6D_6F_64_75_6C_65 } ;
("xmlns:xlink") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_6E_73_3A_78_6C_69_6E_6B } ;
("noframes") => { $ crate :: ATOM_LOCALNAME__6E_6F_66_72_61_6D_65_73 } ;
("altGlyphDef") => { $ crate :: ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68_44_65_66 } ;
("irrelevant") => { $ crate :: ATOM_LOCALNAME__69_72_72_65_6C_65_76_61_6E_74 } ;
("nonce") => { $ crate :: ATOM_LOCALNAME__6E_6F_6E_63_65 } ;
("background") => { $ crate :: ATOM_LOCALNAME__62_61_63_6B_67_72_6F_75_6E_64 } ;
("forall") => { $ crate :: ATOM_LOCALNAME__66_6F_72_61_6C_6C } ;
("xmlns") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_6E_73 } ;
("result") => { $ crate :: ATOM_LOCALNAME__72_65_73_75_6C_74 } ;
("stroke-dasharray") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_64_61_73_68_61_72_72_61_79 } ;
("abs") => { $ crate :: ATOM_LOCALNAME__61_62_73 } ;
("details") => { $ crate :: ATOM_LOCALNAME__64_65_74_61_69_6C_73 } ;
("translate") => { $ crate :: ATOM_LOCALNAME__74_72_61_6E_73_6C_61_74_65 } ;
("equivalent") => { $ crate :: ATOM_LOCALNAME__65_71_75_69_76_61_6C_65_6E_74 } ;
("factorial") => { $ crate :: ATOM_LOCALNAME__66_61_63_74_6F_72_69_61_6C } ;
("prompt") => { $ crate :: ATOM_LOCALNAME__70_72_6F_6D_70_74 } ;
("mathvariant") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_76_61_72_69_61_6E_74 } ;
("times") => { $ crate :: ATOM_LOCALNAME__74_69_6D_65_73 } ;
("requiredExtensions") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64_45_78_74_65_6E_73_69_6F_6E_73 } ;
("stitchTiles") => { $ crate :: ATOM_LOCALNAME__73_74_69_74_63_68_54_69_6C_65_73 } ;
("uplimit") => { $ crate :: ATOM_LOCALNAME__75_70_6C_69_6D_69_74 } ;
("fefuncg") => { $ crate :: ATOM_LOCALNAME__66_65_66_75_6E_63_67 } ;
("spreadmethod") => { $ crate :: ATOM_LOCALNAME__73_70_72_65_61_64_6D_65_74_68_6F_64 } ;
("onrepeat") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_65_70_65_61_74 } ;
("onoffline") => { $ crate :: ATOM_LOCALNAME__6F_6E_6F_66_66_6C_69_6E_65 } ;
("autosubmit") => { $ crate :: ATOM_LOCALNAME__61_75_74_6F_73_75_62_6D_69_74 } ;
("log") => { $ crate :: ATOM_LOCALNAME__6C_6F_67 } ;
("spacer") => { $ crate :: ATOM_LOCALNAME__73_70_61_63_65_72 } ;
("cap-height") => { $ crate :: ATOM_LOCALNAME__63_61_70_2D_68_65_69_67_68_74 } ;
("kernelunitlength") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_65_6C_75_6E_69_74_6C_65_6E_67_74_68 } ;
("onlanguagechange") => { $ crate :: ATOM_LOCALNAME__6F_6E_6C_61_6E_67_75_61_67_65_63_68_61_6E_67_65 } ;
("coords") => { $ crate :: ATOM_LOCALNAME__63_6F_6F_72_64_73 } ;
("infinity") => { $ crate :: ATOM_LOCALNAME__69_6E_66_69_6E_69_74_79 } ;
("bgcolor") => { $ crate :: ATOM_LOCALNAME__62_67_63_6F_6C_6F_72 } ;
("codetype") => { $ crate :: ATOM_LOCALNAME__63_6F_64_65_74_79_70_65 } ;
("keytimes") => { $ crate :: ATOM_LOCALNAME__6B_65_79_74_69_6D_65_73 } ;
("onbeforeprint") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_70_72_69_6E_74 } ;
("overline-position") => { $ crate :: ATOM_LOCALNAME__6F_76_65_72_6C_69_6E_65_2D_70_6F_73_69_74_69_6F_6E } ;
("implies") => { $ crate :: ATOM_LOCALNAME__69_6D_70_6C_69_65_73 } ;
("onrowsinserted") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_6F_77_73_69_6E_73_65_72_74_65_64 } ;
("contentscripttype") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_73_63_72_69_70_74_74_79_70_65 } ;
("head") => { $ crate :: ATOM_LOCALNAME__68_65_61_64 } ;
("valign") => { $ crate :: ATOM_LOCALNAME__76_61_6C_69_67_6E } ;
("name") => { $ crate :: ATOM_LOCALNAME__6E_61_6D_65 } ;
("animateTransform") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_54_72_61_6E_73_66_6F_72_6D } ;
("pointsatx") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_78 } ;
("laplacian") => { $ crate :: ATOM_LOCALNAME__6C_61_70_6C_61_63_69_61_6E } ;
("metadata") => { $ crate :: ATOM_LOCALNAME__6D_65_74_61_64_61_74_61 } ;
("actiontype") => { $ crate :: ATOM_LOCALNAME__61_63_74_69_6F_6E_74_79_70_65 } ;
("pointsAtZ") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_5A } ;
("class") => { $ crate :: ATOM_LOCALNAME__63_6C_61_73_73 } ;
("widths") => { $ crate :: ATOM_LOCALNAME__77_69_64_74_68_73 } ;
("download") => { $ crate :: ATOM_LOCALNAME__64_6F_77_6E_6C_6F_61_64 } ;
("onmouseout") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6F_75_74 } ;
("u1") => { $ crate :: ATOM_LOCALNAME__75_31 } ;
("baseprofile") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_70_72_6F_66_69_6C_65 } ;
("ondataavailable") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_61_74_61_61_76_61_69_6C_61_62_6C_65 } ;
("y") => { $ crate :: ATOM_LOCALNAME__79 } ;
("unselectable") => { $ crate :: ATOM_LOCALNAME__75_6E_73_65_6C_65_63_74_61_62_6C_65 } ;
("or") => { $ crate :: ATOM_LOCALNAME__6F_72 } ;
("mspace") => { $ crate :: ATOM_LOCALNAME__6D_73_70_61_63_65 } ;
("alphabetic") => { $ crate :: ATOM_LOCALNAME__61_6C_70_68_61_62_65_74_69_63 } ;
("targety") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74_79 } ;
("filterunits") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72_75_6E_69_74_73 } ;
("feMergeNode") => { $ crate :: ATOM_LOCALNAME__66_65_4D_65_72_67_65_4E_6F_64_65 } ;
("systemlanguage") => { $ crate :: ATOM_LOCALNAME__73_79_73_74_65_6D_6C_61_6E_67_75_61_67_65 } ;
("onkeypress") => { $ crate :: ATOM_LOCALNAME__6F_6E_6B_65_79_70_72_65_73_73 } ;
("d") => { $ crate :: ATOM_LOCALNAME__64 } ;
("frameborder") => { $ crate :: ATOM_LOCALNAME__66_72_61_6D_65_62_6F_72_64_65_72 } ;
("fePointLight") => { $ crate :: ATOM_LOCALNAME__66_65_50_6F_69_6E_74_4C_69_67_68_74 } ;
("sum") => { $ crate :: ATOM_LOCALNAME__73_75_6D } ;
("onbeforecopy") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_63_6F_70_79 } ;
("onresize") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_65_73_69_7A_65 } ;
("yChannelSelector") => { $ crate :: ATOM_LOCALNAME__79_43_68_61_6E_6E_65_6C_53_65_6C_65_63_74_6F_72 } ;
("sinh") => { $ crate :: ATOM_LOCALNAME__73_69_6E_68 } ;
("i") => { $ crate :: ATOM_LOCALNAME__69 } ;
("ondatasetchanged") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_61_74_61_73_65_74_63_68_61_6E_67_65_64 } ;
("targetX") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74_58 } ;
("variance") => { $ crate :: ATOM_LOCALNAME__76_61_72_69_61_6E_63_65 } ;
("stemv") => { $ crate :: ATOM_LOCALNAME__73_74_65_6D_76 } ;
("big") => { $ crate :: ATOM_LOCALNAME__62_69_67 } ;
("sin") => { $ crate :: ATOM_LOCALNAME__73_69_6E } ;
("filterres") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72_72_65_73 } ;
("mozbrowser") => { $ crate :: ATOM_LOCALNAME__6D_6F_7A_62_72_6F_77_73_65_72 } ;
("ideographic") => { $ crate :: ATOM_LOCALNAME__69_64_65_6F_67_72_61_70_68_69_63 } ;
("onhelp") => { $ crate :: ATOM_LOCALNAME__6F_6E_68_65_6C_70 } ;
("action") => { $ crate :: ATOM_LOCALNAME__61_63_74_69_6F_6E } ;
("longdesc") => { $ crate :: ATOM_LOCALNAME__6C_6F_6E_67_64_65_73_63 } ;
("fx") => { $ crate :: ATOM_LOCALNAME__66_78 } ;
("textarea") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_61_72_65_61 } ;
("intersect") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_72_73_65_63_74 } ;
("specification") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_69_66_69_63_61_74_69_6F_6E } ;
("veryverythinmathspace") => { $ crate :: ATOM_LOCALNAME__76_65_72_79_76_65_72_79_74_68_69_6E_6D_61_74_68_73_70_61_63_65 } ;
("strikethrough-thickness") => { $ crate :: ATOM_LOCALNAME__73_74_72_69_6B_65_74_68_72_6F_75_67_68_2D_74_68_69_63_6B_6E_65_73_73 } ;
("sandbox") => { $ crate :: ATOM_LOCALNAME__73_61_6E_64_62_6F_78 } ;
("oninvalid") => { $ crate :: ATOM_LOCALNAME__6F_6E_69_6E_76_61_6C_69_64 } ;
("foreignObject") => { $ crate :: ATOM_LOCALNAME__66_6F_72_65_69_67_6E_4F_62_6A_65_63_74 } ;
("aria-busy") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_62_75_73_79 } ;
("fetile") => { $ crate :: ATOM_LOCALNAME__66_65_74_69_6C_65 } ;
("flood-opacity") => { $ crate :: ATOM_LOCALNAME__66_6C_6F_6F_64_2D_6F_70_61_63_69_74_79 } ;
("map") => { $ crate :: ATOM_LOCALNAME__6D_61_70 } ;
("text-rendering") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_2D_72_65_6E_64_65_72_69_6E_67 } ;
("onmovestart") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_76_65_73_74_61_72_74 } ;
("circle") => { $ crate :: ATOM_LOCALNAME__63_69_72_63_6C_65 } ;
("button") => { $ crate :: ATOM_LOCALNAME__62_75_74_74_6F_6E } ;
("stroke-miterlimit") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6D_69_74_65_72_6C_69_6D_69_74 } ;
("baseline-shift") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_6C_69_6E_65_2D_73_68_69_66_74 } ;
("notanumber") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_61_6E_75_6D_62_65_72 } ;
("b") => { $ crate :: ATOM_LOCALNAME__62 } ;
("feConvolveMatrix") => { $ crate :: ATOM_LOCALNAME__66_65_43_6F_6E_76_6F_6C_76_65_4D_61_74_72_69_78 } ;
("kerning") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_69_6E_67 } ;
("keyPoints") => { $ crate :: ATOM_LOCALNAME__6B_65_79_50_6F_69_6E_74_73 } ;
("k3") => { $ crate :: ATOM_LOCALNAME__6B_33 } ;
("contextmenu") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_78_74_6D_65_6E_75 } ;
("aria-setsize") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_73_65_74_73_69_7A_65 } ;
("transpose") => { $ crate :: ATOM_LOCALNAME__74_72_61_6E_73_70_6F_73_65 } ;
("listener") => { $ crate :: ATOM_LOCALNAME__6C_69_73_74_65_6E_65_72 } ;
("dir") => { $ crate :: ATOM_LOCALNAME__64_69_72 } ;
("edgemode") => { $ crate :: ATOM_LOCALNAME__65_64_67_65_6D_6F_64_65 } ;
("face") => { $ crate :: ATOM_LOCALNAME__66_61_63_65 } ;
("begin") => { $ crate :: ATOM_LOCALNAME__62_65_67_69_6E } ;
("controllerchange") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_72_6F_6C_6C_65_72_63_68_61_6E_67_65 } ;
("onmouseover") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6F_76_65_72 } ;
("meta") => { $ crate :: ATOM_LOCALNAME__6D_65_74_61 } ;
("malignmark") => { $ crate :: ATOM_LOCALNAME__6D_61_6C_69_67_6E_6D_61_72_6B } ;
("k1") => { $ crate :: ATOM_LOCALNAME__6B_31 } ;
("meter") => { $ crate :: ATOM_LOCALNAME__6D_65_74_65_72 } ;
("plus") => { $ crate :: ATOM_LOCALNAME__70_6C_75_73 } ;
("arccot") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_6F_74 } ;
("mfenced") => { $ crate :: ATOM_LOCALNAME__6D_66_65_6E_63_65_64 } ;
("altglyph") => { $ crate :: ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68 } ;
("aria-templateid") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_74_65_6D_70_6C_61_74_65_69_64 } ;
("xChannelSelector") => { $ crate :: ATOM_LOCALNAME__78_43_68_61_6E_6E_65_6C_53_65_6C_65_63_74_6F_72 } ;
("definitionurl") => { $ crate :: ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_75_72_6C } ;
("text") => { $ crate :: ATOM_LOCALNAME__74_65_78_74 } ;
("srcdoc") => { $ crate :: ATOM_LOCALNAME__73_72_63_64_6F_63 } ;
("onformchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_72_6D_63_68_61_6E_67_65 } ;
("baseFrequency") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_46_72_65_71_75_65_6E_63_79 } ;
("xlink:title") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_74_69_74_6C_65 } ;
("max") => { $ crate :: ATOM_LOCALNAME__6D_61_78 } ;
("rtc") => { $ crate :: ATOM_LOCALNAME__72_74_63 } ;
("tendsto") => { $ crate :: ATOM_LOCALNAME__74_65_6E_64_73_74_6F } ;
("alignmentscope") => { $ crate :: ATOM_LOCALNAME__61_6C_69_67_6E_6D_65_6E_74_73_63_6F_70_65 } ;
("ins") => { $ crate :: ATOM_LOCALNAME__69_6E_73 } ;
("title") => { $ crate :: ATOM_LOCALNAME__74_69_74_6C_65 } ;
("onpropertychange") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_72_6F_70_65_72_74_79_63_68_61_6E_67_65 } ;
("encoding") => { $ crate :: ATOM_LOCALNAME__65_6E_63_6F_64_69_6E_67 } ;
("notprsubset") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_70_72_73_75_62_73_65_74 } ;
("operator") => { $ crate :: ATOM_LOCALNAME__6F_70_65_72_61_74_6F_72 } ;
("underline-thickness") => { $ crate :: ATOM_LOCALNAME__75_6E_64_65_72_6C_69_6E_65_2D_74_68_69_63_6B_6E_65_73_73 } ;
("onbeforepaste") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_70_61_73_74_65 } ;
("fecomponenttransfer") => { $ crate :: ATOM_LOCALNAME__66_65_63_6F_6D_70_6F_6E_65_6E_74_74_72_61_6E_73_66_65_72 } ;
("height") => { $ crate :: ATOM_LOCALNAME__68_65_69_67_68_74 } ;
("solidcolor") => { $ crate :: ATOM_LOCALNAME__73_6F_6C_69_64_63_6F_6C_6F_72 } ;
("femorphology") => { $ crate :: ATOM_LOCALNAME__66_65_6D_6F_72_70_68_6F_6C_6F_67_79 } ;
("vert-origin-y") => { $ crate :: ATOM_LOCALNAME__76_65_72_74_2D_6F_72_69_67_69_6E_2D_79 } ;
("notation") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_61_74_69_6F_6E } ;
("template") => { $ crate :: ATOM_LOCALNAME__74_65_6D_70_6C_61_74_65 } ;
("pre") => { $ crate :: ATOM_LOCALNAME__70_72_65 } ;
("h3") => { $ crate :: ATOM_LOCALNAME__68_33 } ;
("repeat-start") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_2D_73_74_61_72_74 } ;
("seed") => { $ crate :: ATOM_LOCALNAME__73_65_65_64 } ;
("glyphref") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_72_65_66 } ;
("onerrorupdate") => { $ crate :: ATOM_LOCALNAME__6F_6E_65_72_72_6F_72_75_70_64_61_74_65 } ;
("fespecularlighting") => { $ crate :: ATOM_LOCALNAME__66_65_73_70_65_63_75_6C_61_72_6C_69_67_68_74_69_6E_67 } ;
("feflood") => { $ crate :: ATOM_LOCALNAME__66_65_66_6C_6F_6F_64 } ;
("*") => { $ crate :: ATOM_LOCALNAME__2A } ;
("canvas") => { $ crate :: ATOM_LOCALNAME__63_61_6E_76_61_73 } ;
("onselect") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_65_6C_65_63_74 } ;
("pointsAtX") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_58 } ;
("direction") => { $ crate :: ATOM_LOCALNAME__64_69_72_65_63_74_69_6F_6E } ;
("matrixrow") => { $ crate :: ATOM_LOCALNAME__6D_61_74_72_69_78_72_6F_77 } ;
("formenctype") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_65_6E_63_74_79_70_65 } ;
("markerunits") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_75_6E_69_74_73 } ;
("header") => { $ crate :: ATOM_LOCALNAME__68_65_61_64_65_72 } ;
("onkeyup") => { $ crate :: ATOM_LOCALNAME__6F_6E_6B_65_79_75_70 } ;
("lengthadjust") => { $ crate :: ATOM_LOCALNAME__6C_65_6E_67_74_68_61_64_6A_75_73_74 } ;
("mark") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B } ;
("cn") => { $ crate :: ATOM_LOCALNAME__63_6E } ;
("aria-valuenow") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6E_6F_77 } ;
("markerwidth") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_77_69_64_74_68 } ;
("ascent") => { $ crate :: ATOM_LOCALNAME__61_73_63_65_6E_74 } ;
("show") => { $ crate :: ATOM_LOCALNAME__73_68_6F_77 } ;
("param") => { $ crate :: ATOM_LOCALNAME__70_61_72_61_6D } ;
("pointsaty") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_79 } ;
("msqrt") => { $ crate :: ATOM_LOCALNAME__6D_73_71_72_74 } ;
("readonly") => { $ crate :: ATOM_LOCALNAME__72_65_61_64_6F_6E_6C_79 } ;
("feImage") => { $ crate :: ATOM_LOCALNAME__66_65_49_6D_61_67_65 } ;
("patternunits") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_75_6E_69_74_73 } ;
("spacing") => { $ crate :: ATOM_LOCALNAME__73_70_61_63_69_6E_67 } ;
("word-spacing") => { $ crate :: ATOM_LOCALNAME__77_6F_72_64_2D_73_70_61_63_69_6E_67 } ;
("marquee") => { $ crate :: ATOM_LOCALNAME__6D_61_72_71_75_65_65 } ;
("colgroup") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_67_72_6F_75_70 } ;
("onactivate") => { $ crate :: ATOM_LOCALNAME__6F_6E_61_63_74_69_76_61_74_65 } ;
("stroke-width") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_77_69_64_74_68 } ;
("annotation-xml") => { $ crate :: ATOM_LOCALNAME__61_6E_6E_6F_74_61_74_69_6F_6E_2D_78_6D_6C } ;
("aria-hidden") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_68_69_64_64_65_6E } ;
("requiredextensions") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64_65_78_74_65_6E_73_69_6F_6E_73 } ;
("charset") => { $ crate :: ATOM_LOCALNAME__63_68_61_72_73_65_74 } ;
("movablelimits") => { $ crate :: ATOM_LOCALNAME__6D_6F_76_61_62_6C_65_6C_69_6D_69_74_73 } ;
("stroke-linecap") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6C_69_6E_65_63_61_70 } ;
("enable-background") => { $ crate :: ATOM_LOCALNAME__65_6E_61_62_6C_65_2D_62_61_63_6B_67_72_6F_75_6E_64 } ;
("onstop") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_74_6F_70 } ;
("size") => { $ crate :: ATOM_LOCALNAME__73_69_7A_65 } ;
("momentabout") => { $ crate :: ATOM_LOCALNAME__6D_6F_6D_65_6E_74_61_62_6F_75_74 } ;
("g") => { $ crate :: ATOM_LOCALNAME__67 } ;
("basefont") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_66_6F_6E_74 } ;
("u") => { $ crate :: ATOM_LOCALNAME__75 } ;
("not") => { $ crate :: ATOM_LOCALNAME__6E_6F_74 } ;
("h4") => { $ crate :: ATOM_LOCALNAME__68_34 } ;
("linethickness") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_74_68_69_63_6B_6E_65_73_73 } ;
("aria-relevant") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_72_65_6C_65_76_61_6E_74 } ;
("linebreak") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_62_72_65_61_6B } ;
("onmouseleave") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6C_65_61_76_65 } ;
("xml:lang") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_3A_6C_61_6E_67 } ;
("overline-thickness") => { $ crate :: ATOM_LOCALNAME__6F_76_65_72_6C_69_6E_65_2D_74_68_69_63_6B_6E_65_73_73 } ;
("xor") => { $ crate :: ATOM_LOCALNAME__78_6F_72 } ;
("rect") => { $ crate :: ATOM_LOCALNAME__72_65_63_74 } ;
("mmultiscripts") => { $ crate :: ATOM_LOCALNAME__6D_6D_75_6C_74_69_73_63_72_69_70_74_73 } ;
("onrowsdelete") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_6F_77_73_64_65_6C_65_74_65 } ;
("bdo") => { $ crate :: ATOM_LOCALNAME__62_64_6F } ;
("accumulate") => { $ crate :: ATOM_LOCALNAME__61_63_63_75_6D_75_6C_61_74_65 } ;
("section") => { $ crate :: ATOM_LOCALNAME__73_65_63_74_69_6F_6E } ;
("divide") => { $ crate :: ATOM_LOCALNAME__64_69_76_69_64_65 } ;
("img") => { $ crate :: ATOM_LOCALNAME__69_6D_67 } ;
("lambda") => { $ crate :: ATOM_LOCALNAME__6C_61_6D_62_64_61 } ;
("gradientunits") => { $ crate :: ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_75_6E_69_74_73 } ;
("preservealpha") => { $ crate :: ATOM_LOCALNAME__70_72_65_73_65_72_76_65_61_6C_70_68_61 } ;
("small") => { $ crate :: ATOM_LOCALNAME__73_6D_61_6C_6C } ;
("frameset") => { $ crate :: ATOM_LOCALNAME__66_72_61_6D_65_73_65_74 } ;
("var") => { $ crate :: ATOM_LOCALNAME__76_61_72 } ;
("content") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74 } ;
("attributeName") => { $ crate :: ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_4E_61_6D_65 } ;
("onend") => { $ crate :: ATOM_LOCALNAME__6F_6E_65_6E_64 } ;
("thinmathspace") => { $ crate :: ATOM_LOCALNAME__74_68_69_6E_6D_61_74_68_73_70_61_63_65 } ;
("viewbox") => { $ crate :: ATOM_LOCALNAME__76_69_65_77_62_6F_78 } ;
("marker-start") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_73_74_61_72_74 } ;
("glyphRef") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_52_65_66 } ;
("attributename") => { $ crate :: ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_6E_61_6D_65 } ;
("alt") => { $ crate :: ATOM_LOCALNAME__61_6C_74 } ;
("cite") => { $ crate :: ATOM_LOCALNAME__63_69_74_65 } ;
("rowalign") => { $ crate :: ATOM_LOCALNAME__72_6F_77_61_6C_69_67_6E } ;
("externalResourcesRequired") => { $ crate :: ATOM_LOCALNAME__65_78_74_65_72_6E_61_6C_52_65_73_6F_75_72_63_65_73_52_65_71_75_69_72_65_64 } ;
("feDiffuseLighting") => { $ crate :: ATOM_LOCALNAME__66_65_44_69_66_66_75_73_65_4C_69_67_68_74_69_6E_67 } ;
("video") => { $ crate :: ATOM_LOCALNAME__76_69_64_65_6F } ;
("rowlines") => { $ crate :: ATOM_LOCALNAME__72_6F_77_6C_69_6E_65_73 } ;
("stdDeviation") => { $ crate :: ATOM_LOCALNAME__73_74_64_44_65_76_69_61_74_69_6F_6E } ;
("compact") => { $ crate :: ATOM_LOCALNAME__63_6F_6D_70_61_63_74 } ;
("hidefocus") => { $ crate :: ATOM_LOCALNAME__68_69_64_65_66_6F_63_75_73 } ;
("hanging") => { $ crate :: ATOM_LOCALNAME__68_61_6E_67_69_6E_67 } ;
("separators") => { $ crate :: ATOM_LOCALNAME__73_65_70_61_72_61_74_6F_72_73 } ;
("scriptminsize") => { $ crate :: ATOM_LOCALNAME__73_63_72_69_70_74_6D_69_6E_73_69_7A_65 } ;
("base") => { $ crate :: ATOM_LOCALNAME__62_61_73_65 } ;
("col") => { $ crate :: ATOM_LOCALNAME__63_6F_6C } ;
("points") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73 } ;
("tbody") => { $ crate :: ATOM_LOCALNAME__74_62_6F_64_79 } ;
("onclick") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_6C_69_63_6B } ;
("fediffuselighting") => { $ crate :: ATOM_LOCALNAME__66_65_64_69_66_66_75_73_65_6C_69_67_68_74_69_6E_67 } ;
("aria-atomic") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_61_74_6F_6D_69_63 } ;
("strikethrough-position") => { $ crate :: ATOM_LOCALNAME__73_74_72_69_6B_65_74_68_72_6F_75_67_68_2D_70_6F_73_69_74_69_6F_6E } ;
("textlength") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_6C_65_6E_67_74_68 } ;
("aria-live") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6C_69_76_65 } ;
("azimuth") => { $ crate :: ATOM_LOCALNAME__61_7A_69_6D_75_74_68 } ;
("radialgradient") => { $ crate :: ATOM_LOCALNAME__72_61_64_69_61_6C_67_72_61_64_69_65_6E_74 } ;
("feTile") => { $ crate :: ATOM_LOCALNAME__66_65_54_69_6C_65 } ;
("keySplines") => { $ crate :: ATOM_LOCALNAME__6B_65_79_53_70_6C_69_6E_65_73 } ;
("classid") => { $ crate :: ATOM_LOCALNAME__63_6C_61_73_73_69_64 } ;
("restart") => { $ crate :: ATOM_LOCALNAME__72_65_73_74_61_72_74 } ;
("ondeactivate") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_65_61_63_74_69_76_61_74_65 } ;
("clipPathUnits") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_50_61_74_68_55_6E_69_74_73 } ;
("reln") => { $ crate :: ATOM_LOCALNAME__72_65_6C_6E } ;
("arcsin") => { $ crate :: ATOM_LOCALNAME__61_72_63_73_69_6E } ;
("disabled") => { $ crate :: ATOM_LOCALNAME__64_69_73_61_62_6C_65_64 } ;
("caption") => { $ crate :: ATOM_LOCALNAME__63_61_70_74_69_6F_6E } ;
("patterntransform") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_74_72_61_6E_73_66_6F_72_6D } ;
("aria-readonly") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_72_65_61_64_6F_6E_6C_79 } ;
("shape") => { $ crate :: ATOM_LOCALNAME__73_68_61_70_65 } ;
("noshade") => { $ crate :: ATOM_LOCALNAME__6E_6F_73_68_61_64_65 } ;
("v-mathematical") => { $ crate :: ATOM_LOCALNAME__76_2D_6D_61_74_68_65_6D_61_74_69_63_61_6C } ;
("mpadded") => { $ crate :: ATOM_LOCALNAME__6D_70_61_64_64_65_64 } ;
("depth") => { $ crate :: ATOM_LOCALNAME__64_65_70_74_68 } ;
("feOffset") => { $ crate :: ATOM_LOCALNAME__66_65_4F_66_66_73_65_74 } ;
("keypoints") => { $ crate :: ATOM_LOCALNAME__6B_65_79_70_6F_69_6E_74_73 } ;
("y1") => { $ crate :: ATOM_LOCALNAME__79_31 } ;
("fefuncr") => { $ crate :: ATOM_LOCALNAME__66_65_66_75_6E_63_72 } ;
("divisor") => { $ crate :: ATOM_LOCALNAME__64_69_76_69_73_6F_72 } ;
("marginwidth") => { $ crate :: ATOM_LOCALNAME__6D_61_72_67_69_6E_77_69_64_74_68 } ;
("hgroup") => { $ crate :: ATOM_LOCALNAME__68_67_72_6F_75_70 } ;
("arccosh") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_6F_73_68 } ;
("cot") => { $ crate :: ATOM_LOCALNAME__63_6F_74 } ;
("datalist") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_6C_69_73_74 } ;
("glyph") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68 } ;
("kernelMatrix") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_65_6C_4D_61_74_72_69_78 } ;
("attributeType") => { $ crate :: ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_54_79_70_65 } ;
("setdiff") => { $ crate :: ATOM_LOCALNAME__73_65_74_64_69_66_66 } ;
("u2") => { $ crate :: ATOM_LOCALNAME__75_32 } ;
("ondragleave") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_6C_65_61_76_65 } ;
("scrolling") => { $ crate :: ATOM_LOCALNAME__73_63_72_6F_6C_6C_69_6E_67 } ;
("zoomAndPan") => { $ crate :: ATOM_LOCALNAME__7A_6F_6F_6D_41_6E_64_50_61_6E } ;
("by") => { $ crate :: ATOM_LOCALNAME__62_79 } ;
("diff") => { $ crate :: ATOM_LOCALNAME__64_69_66_66 } ;
}pub type Prefix = :: string_cache :: Atom < PrefixStaticSet > ;
# [derive (PartialEq , Eq , PartialOrd , Ord)] pub struct PrefixStaticSet ;
impl :: string_cache :: StaticAtomSet for PrefixStaticSet { fn get () -> & 'static :: string_cache :: PhfStrSet { static SET : :: string_cache :: PhfStrSet = :: string_cache :: PhfStrSet { key : 15467950696543387533u64 , disps : & [(1u32 , 0u32) , (0u32 , 6u32)] , atoms : & ["*",
"html",
"xml",
"",
"svg",
"mathml",
"xlink",
"xmlns"] , hashes : & [1087184163u32 , 57765587u32 , 4084850464u32 , 811901650u32 , 2480327709u32 , 2091895163u32 , 2327862095u32 , 3256068999u32] } ;
& SET } fn empty_string_index () -> u32 { 3u32 } } pub const ATOM_PREFIX__2A : Prefix = Prefix :: pack_static (0u32) ;
pub const ATOM_PREFIX__68_74_6D_6C : Prefix = Prefix :: pack_static (1u32) ;
pub const ATOM_PREFIX__78_6D_6C : Prefix = Prefix :: pack_static (2u32) ;
pub const ATOM_PREFIX_ : Prefix = Prefix :: pack_static (3u32) ;
pub const ATOM_PREFIX__73_76_67 : Prefix = Prefix :: pack_static (4u32) ;
pub const ATOM_PREFIX__6D_61_74_68_6D_6C : Prefix = Prefix :: pack_static (5u32) ;
pub const ATOM_PREFIX__78_6C_69_6E_6B : Prefix = Prefix :: pack_static (6u32) ;
pub const ATOM_PREFIX__78_6D_6C_6E_73 : Prefix = Prefix :: pack_static (7u32) ;
# [doc = "Takes a namespace prefix string and returns its key in a string cache."] # [macro_export] macro_rules ! namespace_prefix { ("*") => { $ crate :: ATOM_PREFIX__2A } ;
("html") => { $ crate :: ATOM_PREFIX__68_74_6D_6C } ;
("xml") => { $ crate :: ATOM_PREFIX__78_6D_6C } ;
("") => { $ crate :: ATOM_PREFIX_ } ;
("svg") => { $ crate :: ATOM_PREFIX__73_76_67 } ;
("mathml") => { $ crate :: ATOM_PREFIX__6D_61_74_68_6D_6C } ;
("xlink") => { $ crate :: ATOM_PREFIX__78_6C_69_6E_6B } ;
("xmlns") => { $ crate :: ATOM_PREFIX__78_6D_6C_6E_73 } ;
}pub type Namespace = :: string_cache :: Atom < NamespaceStaticSet > ;
# [derive (PartialEq , Eq , PartialOrd , Ord)] pub struct NamespaceStaticSet ;
impl :: string_cache :: StaticAtomSet for NamespaceStaticSet { fn get () -> & 'static :: string_cache :: PhfStrSet { static SET : :: string_cache :: PhfStrSet = :: string_cache :: PhfStrSet { key : 345707026197253659u64 , disps : & [(0u32 , 7u32) , (6u32 , 0u32)] , atoms : & ["http://www.w3.org/1999/xlink",
"http://www.w3.org/1998/Math/MathML",
"http://www.w3.org/XML/1998/namespace",
"*",
"http://www.w3.org/2000/svg",
"http://www.w3.org/2000/xmlns/",
"",
"http://www.w3.org/1999/xhtml"] , hashes : & [2476384262u32 , 858104709u32 , 4140503004u32 , 1559514276u32 , 3566477928u32 , 1845400423u32 , 577764079u32 , 791746902u32] } ;
& SET } fn empty_string_index () -> u32 { 6u32 } } pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_39_2F_78_6C_69_6E_6B : Namespace = Namespace :: pack_static (0u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_38_2F_4D_61_74_68_2F_4D_61_74_68_4D_4C : Namespace = Namespace :: pack_static (1u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_58_4D_4C_2F_31_39_39_38_2F_6E_61_6D_65_73_70_61_63_65 : Namespace = Namespace :: pack_static (2u32) ;
pub const ATOM_NAMESPACE__2A : Namespace = Namespace :: pack_static (3u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_32_30_30_30_2F_73_76_67 : Namespace = Namespace :: pack_static (4u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_32_30_30_30_2F_78_6D_6C_6E_73_2F : Namespace = Namespace :: pack_static (5u32) ;
pub const ATOM_NAMESPACE_ : Namespace = Namespace :: pack_static (6u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_39_2F_78_68_74_6D_6C : Namespace = Namespace :: pack_static (7u32) ;
# [doc = "Takes a namespace url string and returns its key in a string cache."] # [macro_export] macro_rules ! namespace_url { ("http://www.w3.org/1999/xlink") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_39_2F_78_6C_69_6E_6B } ;
("http://www.w3.org/1998/Math/MathML") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_38_2F_4D_61_74_68_2F_4D_61_74_68_4D_4C } ;
("http://www.w3.org/XML/1998/namespace") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_58_4D_4C_2F_31_39_39_38_2F_6E_61_6D_65_73_70_61_63_65 } ;
("*") => { $ crate :: ATOM_NAMESPACE__2A } ;
("http://www.w3.org/2000/svg") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_32_30_30_30_2F_73_76_67 } ;
("http://www.w3.org/2000/xmlns/") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_32_30_30_30_2F_78_6D_6C_6E_73_2F } ;
("") => { $ crate :: ATOM_NAMESPACE_ } ;
("http://www.w3.org/1999/xhtml") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_39_2F_78_68_74_6D_6C } ;
}
        /// Maps the input of [`namespace_prefix!`](macro.namespace_prefix.html) to 
        /// the output of [`namespace_url!`](macro.namespace_url.html).
        ///
        #[macro_export] macro_rules! ns {
        
() => { namespace_url!("") };
(*) => { namespace_url!("*") };
(html) => { namespace_url!("http://www.w3.org/1999/xhtml") };
(xml) => { namespace_url!("http://www.w3.org/XML/1998/namespace") };
(xmlns) => { namespace_url!("http://www.w3.org/2000/xmlns/") };
(xlink) => { namespace_url!("http://www.w3.org/1999/xlink") };
(svg) => { namespace_url!("http://www.w3.org/2000/svg") };
(mathml) => { namespace_url!("http://www.w3.org/1998/Math/MathML") };
}
