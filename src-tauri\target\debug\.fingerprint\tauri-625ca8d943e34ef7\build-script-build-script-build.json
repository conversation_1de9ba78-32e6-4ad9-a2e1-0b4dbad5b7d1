{"rustc": 208723055495880444, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"objc-exception\", \"tauri-runtime-wry\", \"test\", \"wry\"]", "declared_features": "", "target": 427768481117760528, "profile": 13232757476167777671, "path": 13294390773289531385, "deps": [[15200162540926943430, "tauri_build", false, 11859339631427923386], [16448600290378106715, "tauri_utils", false, 11858062004849261349], [17175234422038868540, "heck", false, 6778621052161944775]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-625ca8d943e34ef7\\dep-build-script-build-script-build"}}], "rustflags": [], "metadata": 8230821343879830340, "config": 2202906307356721367, "compile_kind": 0}