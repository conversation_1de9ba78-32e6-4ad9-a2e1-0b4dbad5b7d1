# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-maximizable"
description = "Enables the is_maximizable command without any pre-configured scope."
commands.allow = ["is_maximizable"]

[[permission]]
identifier = "deny-is-maximizable"
description = "Denies the is_maximizable command without any pre-configured scope."
commands.deny = ["is_maximizable"]
