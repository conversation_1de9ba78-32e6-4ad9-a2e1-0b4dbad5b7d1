{"rustc": 208723055495880444, "features": "[]", "declared_features": "", "target": 8519395423344511915, "profile": 12206360443249279867, "path": 10193081248567625933, "deps": [[1682911307880442015, "windows", false, 2220709984778482068], [2032613953716128100, "serde_json", false, 16574326615704706222], [6410343819635645113, "raw_window_handle", false, 10163706409600158415], [8147480371320816545, "http", false, 6980800015614215991], [10420248128255888904, "dpi", false, 5436227026480241103], [12093448282105883233, "build_script_build", false, 16694553548001979690], [13045448243684967688, "serde", false, 12984503464313176728], [13546252512119776598, "thiserror", false, 11763362234821584172], [16448600290378106715, "tauri_utils", false, 1136064509771132172], [18130989770956114225, "url", false, 18271022038104579924]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-15e114c36878a696\\dep-lib-tauri_runtime"}}], "rustflags": [], "metadata": 12594724888323783112, "config": 2202906307356721367, "compile_kind": 0}