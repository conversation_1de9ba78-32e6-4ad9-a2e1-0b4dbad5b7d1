cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=E:\dev\dev_2025\site-z\src-tauri\target\debug\build\tauri-plugin-fs-fa9333eee4de1831\out\tauri-plugin-fs-permission-files
cargo:GLOBAL_SCOPE_SCHEMA_PATH=E:\dev\dev_2025\site-z\src-tauri\target\debug\build\tauri-plugin-fs-fa9333eee4de1831\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\mirrors.aliyun.com-8754fae0eb2f08f1\tauri-plugin-fs-2.2.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
