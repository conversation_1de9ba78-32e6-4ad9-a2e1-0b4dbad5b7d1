{"rustc": 208723055495880444, "features": "[\"objc-exception\"]", "declared_features": "", "target": 11657096340277591349, "profile": 12206360443249279867, "path": 7849264258595679084, "deps": [[1396646470159665871, "tao", false, 14494323176769615201], [1682911307880442015, "windows", false, 2220709984778482068], [5008698008990973163, "webview2_com", false, 11875709066222306240], [6410343819635645113, "raw_window_handle", false, 10163706409600158415], [8147480371320816545, "http", false, 6980800015614215991], [11569250352300043056, "build_script_build", false, 16064248277429831885], [12093448282105883233, "tauri_runtime", false, 2720678577326336479], [15889833344751185223, "wry", false, 13925846734099068688], [16448600290378106715, "tauri_utils", false, 1136064509771132172], [16731215249320019310, "softbuffer", false, 3871193937382834093], [17157248321732533341, "log", false, 6883941472607011862], [18130989770956114225, "url", false, 18271022038104579924]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-d209075e63c0f352\\dep-lib-tauri_runtime_wry"}}], "rustflags": [], "metadata": 6527887627390916949, "config": 2202906307356721367, "compile_kind": 0}