{"rustc": 15597765236515928571, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 13241634480800416876, "deps": [[555019317135488525, "regex_automata", false, 15488332017061954230], [2779309023524819297, "aho_corasick", false, 2393627741273703540], [3129130049864710036, "memchr", false, 10792801857984274433], [9408802513701742484, "regex_syntax", false, 15424056380917401347]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-054267695ddcc422/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}