{"rustc": 15597765236515928571, "features": "[\"std\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 5527823718377024786, "deps": [[2877347214279964928, "pin_project_lite", false, 11190662260366986779], [11033263105862272874, "tracing_core", false, 14410350433340450265]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-be799d1246e8953a/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}