{"rustc": 15597765236515928571, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 12301657607982069544, "profile": 2225463790103693989, "path": 7665362216434764138, "deps": [[79555249104361687, "dyn_clone", false, 1811447268866355260], [494707368875582417, "schemars_derive", false, 16640398756526806294], [666257361909484702, "build_script_build", false, 15541178527040321407], [3150220818285335163, "url", false, 5324281878154911540], [7114545638258605879, "serde", false, 12269381824596681476], [8324636962323428845, "serde_json", false, 409863574066419817], [14923790796823607459, "indexmap", false, 3253364532738967982], [16829695972291065638, "uuid1", false, 12035198319033225486]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/schemars-ed95b2f84e603d0e/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}