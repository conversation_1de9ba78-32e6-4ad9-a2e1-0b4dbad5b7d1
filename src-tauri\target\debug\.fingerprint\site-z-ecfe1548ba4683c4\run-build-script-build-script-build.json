{"rustc": 208723055495880444, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2804010673367980684, "build_script_build", false, 14635941479574557782], [13605424021323096015, "build_script_build", false, 232076185315230483], [17239451597456829448, "build_script_build", false, 5991239809013213812], [5608226185587049571, "build_script_build", false, 11123833500031528772], [12858384651237079900, "build_script_build", false, 4590946373236220397]], "local": [{"RerunIfChanged": {"output": "debug\\build\\site-z-ecfe1548ba4683c4\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}